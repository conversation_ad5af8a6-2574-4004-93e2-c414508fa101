"""
测试修复后的消融实验模型
"""
import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset
from ablation_models import create_ablation_model
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_fixed_ablation_models():
    """测试修复后的消融实验模型"""
    print("🔧 测试修复后的消融实验模型")
    print("=" * 50)
    
    # 创建小规模测试数据
    test_data = ConstellationSMPDataset(
        size=10,  # 小规模测试
        num_samples=5,
        seed=12345,
        memory_total=0.3,
        power_total=5.0,
        num_satellites=3
    )
    
    # 获取一个批次的数据
    static, dynamic, x0 = test_data[0]
    static = static.unsqueeze(0).to(device)  # 添加批次维度
    dynamic = dynamic.unsqueeze(0).to(device)
    
    print(f"输入数据形状:")
    print(f"  static: {static.shape}")
    print(f"  dynamic: {dynamic.shape}")
    
    models_to_test = ['gpn_lstm', 'pn_indrnn']
    
    for model_name in models_to_test:
        print(f"\n测试 {model_name} 模型:")
        try:
            model = create_ablation_model(
                model_name,
                args.static_size, args.dynamic_size, args.hidden_size,
                args.num_satellites, args.rnn, args.num_layers,
                test_data.update_dynamic, test_data.update_mask,
                10, args.dropout, 'cooperative', False, None
            ).to(device)
            
            model.eval()
            with torch.no_grad():
                tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = model(static, dynamic)
                
                print(f"✅ {model_name} 前向传播成功")
                print(f"  tour_indices: {tour_indices.shape}")
                print(f"  satellite_indices: {satellite_indices.shape}")
                print(f"  tour_log_prob: {tour_log_prob.shape}")
                print(f"  satellite_log_prob: {satellite_log_prob.shape}")
                
                # 检查输出的合理性
                assert tour_indices.shape[0] == 1, "批次大小应为1"
                assert satellite_indices.shape[0] == 1, "批次大小应为1"
                assert tour_log_prob.shape[0] == 1, "批次大小应为1"
                assert satellite_log_prob.shape[0] == 1, "批次大小应为1"
                
                print(f"  ✓ 输出维度检查通过")
                
                # 检查数值范围
                print(f"  tour_indices 范围: [{tour_indices.min().item()}, {tour_indices.max().item()}]")
                print(f"  satellite_indices 范围: [{satellite_indices.min().item()}, {satellite_indices.max().item()}]")
                print(f"  tour_log_prob 范围: [{tour_log_prob.min().item():.4f}, {tour_log_prob.max().item():.4f}]")
                print(f"  satellite_log_prob 范围: [{satellite_log_prob.min().item():.4f}, {satellite_log_prob.max().item():.4f}]")
                
        except Exception as e:
            print(f"❌ {model_name} 前向传播失败: {e}")
            import traceback
            traceback.print_exc()

def test_mask_fn_call():
    """测试mask_fn调用是否正确"""
    print(f"\n🔍 测试mask_fn调用")
    print("-" * 30)
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=5,
        num_samples=1,
        seed=12345,
        memory_total=0.3,
        power_total=5.0,
        num_satellites=3
    )
    
    static, dynamic, x0 = test_data[0]
    static = static.unsqueeze(0).to(device)
    dynamic = dynamic.unsqueeze(0).to(device)
    
    print(f"测试数据形状:")
    print(f"  static: {static.shape}")
    print(f"  dynamic: {dynamic.shape}")
    
    # 直接测试mask_fn调用
    try:
        mask, satellite_masks = test_data.update_mask(dynamic, static=static)
        print(f"✅ mask_fn调用成功")
        print(f"  mask: {mask.shape}")
        print(f"  satellite_masks: {satellite_masks.shape}")
        print(f"  mask 值范围: [{mask.min().item()}, {mask.max().item()}]")
        
    except Exception as e:
        print(f"❌ mask_fn调用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_mask_fn_call()
    test_fixed_ablation_models()
    
    print("\n" + "=" * 50)
    print("✅ 消融实验模型修复测试完成！")
    print("如果所有测试通过，可以重新运行训练脚本。")
    print("=" * 50)
