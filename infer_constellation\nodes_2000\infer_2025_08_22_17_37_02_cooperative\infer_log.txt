推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 716.7579
  收益率: 0.9999
  距离: 335.8807
  内存使用: 9.6978
  能量使用: 9.9051
  推理时间: 2.5532秒

批次 2:
  奖励值: 722.3093
  收益率: 0.9999
  距离: 329.0977
  内存使用: 9.7103
  能量使用: 9.9726
  推理时间: 2.6535秒

批次 3:
  奖励值: 729.5312
  收益率: 0.9999
  距离: 326.6256
  内存使用: 9.7315
  能量使用: 9.7805
  推理时间: 2.4171秒

批次 4:
  奖励值: 738.4539
  收益率: 0.9999
  距离: 335.6581
  内存使用: 9.4468
  能量使用: 9.9868
  推理时间: 2.4736秒

批次 5:
  奖励值: 713.7392
  收益率: 0.9999
  距离: 327.9141
  内存使用: 9.6420
  能量使用: 9.9659
  推理时间: 2.3940秒

批次 6:
  奖励值: 729.2001
  收益率: 0.9999
  距离: 335.8473
  内存使用: 9.7409
  能量使用: 10.0108
  推理时间: 2.4101秒

批次 7:
  奖励值: 730.5273
  收益率: 0.9999
  距离: 337.1609
  内存使用: 9.6516
  能量使用: 10.1322
  推理时间: 2.4392秒

批次 8:
  奖励值: 731.0817
  收益率: 0.9999
  距离: 328.5923
  内存使用: 9.6042
  能量使用: 9.8388
  推理时间: 2.4411秒

批次 9:
  奖励值: 734.3745
  收益率: 0.9999
  距离: 339.9615
  内存使用: 9.6027
  能量使用: 9.8858
  推理时间: 2.5013秒

批次 10:
  奖励值: 725.0372
  收益率: 0.9999
  距离: 328.9238
  内存使用: 9.6879
  能量使用: 10.1137
  推理时间: 2.4776秒

批次 11:
  奖励值: 711.9268
  收益率: 0.9999
  距离: 327.4427
  内存使用: 9.7995
  能量使用: 9.9047
  推理时间: 2.4536秒

批次 12:
  奖励值: 719.0968
  收益率: 0.9999
  距离: 337.2976
  内存使用: 9.6613
  能量使用: 10.0485
  推理时间: 2.4798秒

批次 13:
  奖励值: 727.6546
  收益率: 0.9999
  距离: 332.7220
  内存使用: 9.3592
  能量使用: 10.0097
  推理时间: 2.4495秒

批次 14:
  奖励值: 717.7971
  收益率: 0.9999
  距离: 340.4566
  内存使用: 9.6264
  能量使用: 9.9236
  推理时间: 2.5005秒

批次 15:
  奖励值: 730.6924
  收益率: 0.9999
  距离: 327.8163
  内存使用: 9.7982
  能量使用: 10.0010
  推理时间: 2.4542秒

批次 16:
  奖励值: 732.2162
  收益率: 0.9999
  距离: 335.1859
  内存使用: 9.7688
  能量使用: 10.0122
  推理时间: 2.4555秒

批次 17:
  奖励值: 726.5595
  收益率: 0.9999
  距离: 332.9965
  内存使用: 9.3804
  能量使用: 10.1035
  推理时间: 2.3810秒

批次 18:
  奖励值: 728.1389
  收益率: 0.9999
  距离: 325.4669
  内存使用: 9.8281
  能量使用: 10.0288
  推理时间: 2.4052秒

批次 19:
  奖励值: 718.3255
  收益率: 0.9999
  距离: 335.0785
  内存使用: 9.5184
  能量使用: 9.9521
  推理时间: 2.3733秒

批次 20:
  奖励值: 716.5473
  收益率: 0.9999
  距离: 329.0979
  内存使用: 9.5883
  能量使用: 10.2192
  推理时间: 2.3671秒

批次 21:
  奖励值: 715.0419
  收益率: 0.9999
  距离: 324.7634
  内存使用: 9.6167
  能量使用: 10.1356
  推理时间: 2.4615秒

批次 22:
  奖励值: 710.2486
  收益率: 0.9999
  距离: 329.8415
  内存使用: 9.7853
  能量使用: 10.0767
  推理时间: 2.4276秒

批次 23:
  奖励值: 733.0602
  收益率: 0.9999
  距离: 334.2539
  内存使用: 9.6881
  能量使用: 9.7369
  推理时间: 2.4804秒

批次 24:
  奖励值: 730.9269
  收益率: 0.9999
  距离: 334.9806
  内存使用: 9.7294
  能量使用: 10.0363
  推理时间: 2.3941秒

批次 25:
  奖励值: 721.8287
  收益率: 0.9999
  距离: 341.4074
  内存使用: 9.7481
  能量使用: 9.9878
  推理时间: 2.3835秒

批次 26:
  奖励值: 709.6612
  收益率: 0.9999
  距离: 334.3282
  内存使用: 9.6130
  能量使用: 9.9367
  推理时间: 2.4385秒

批次 27:
  奖励值: 725.4220
  收益率: 0.9999
  距离: 338.0471
  内存使用: 9.9159
  能量使用: 9.7938
  推理时间: 2.4353秒

批次 28:
  奖励值: 741.7204
  收益率: 0.9999
  距离: 329.0385
  内存使用: 9.7276
  能量使用: 9.9932
  推理时间: 2.4712秒

批次 29:
  奖励值: 714.4385
  收益率: 0.9999
  距离: 343.5008
  内存使用: 9.6465
  能量使用: 10.1753
  推理时间: 2.4286秒

批次 30:
  奖励值: 729.8073
  收益率: 0.9999
  距离: 332.2012
  内存使用: 9.7753
  能量使用: 10.0094
  推理时间: 2.7077秒

批次 31:
  奖励值: 717.2783
  收益率: 0.9999
  距离: 338.9992
  内存使用: 9.6468
  能量使用: 9.9968
  推理时间: 2.4255秒

批次 32:
  奖励值: 731.4218
  收益率: 0.9999
  距离: 325.2955
  内存使用: 9.7948
  能量使用: 10.4650
  推理时间: 2.4270秒

批次 33:
  奖励值: 724.5232
  收益率: 0.9999
  距离: 333.1931
  内存使用: 9.5538
  能量使用: 9.8077
  推理时间: 2.4645秒

批次 34:
  奖励值: 726.6157
  收益率: 0.9999
  距离: 342.2936
  内存使用: 9.5022
  能量使用: 10.1726
  推理时间: 2.3507秒

批次 35:
  奖励值: 744.3436
  收益率: 0.9999
  距离: 328.4928
  内存使用: 9.6530
  能量使用: 9.9665
  推理时间: 2.6296秒

批次 36:
  奖励值: 734.5712
  收益率: 0.9999
  距离: 332.2466
  内存使用: 9.7194
  能量使用: 9.8926
  推理时间: 2.4109秒

批次 37:
  奖励值: 713.1327
  收益率: 0.9999
  距离: 333.4785
  内存使用: 9.5180
  能量使用: 10.1390
  推理时间: 2.4158秒

批次 38:
  奖励值: 706.8743
  收益率: 0.9999
  距离: 332.5707
  内存使用: 9.7904
  能量使用: 9.8899
  推理时间: 2.3825秒

批次 39:
  奖励值: 721.2322
  收益率: 0.9999
  距离: 327.4982
  内存使用: 9.5822
  能量使用: 9.6540
  推理时间: 2.4310秒

批次 40:
  奖励值: 742.1571
  收益率: 0.9999
  距离: 339.0309
  内存使用: 9.7035
  能量使用: 9.9510
  推理时间: 2.4888秒

批次 41:
  奖励值: 730.2081
  收益率: 0.9999
  距离: 332.9812
  内存使用: 9.6367
  能量使用: 10.3656
  推理时间: 2.3699秒

批次 42:
  奖励值: 726.3423
  收益率: 0.9999
  距离: 340.3602
  内存使用: 9.7352
  能量使用: 10.0201
  推理时间: 2.3936秒

批次 43:
  奖励值: 737.8571
  收益率: 0.9999
  距离: 325.8261
  内存使用: 9.8972
  能量使用: 10.1630
  推理时间: 2.4520秒

批次 44:
  奖励值: 725.3164
  收益率: 0.9999
  距离: 335.6868
  内存使用: 9.5450
  能量使用: 10.1359
  推理时间: 2.4814秒

批次 45:
  奖励值: 726.3497
  收益率: 0.9999
  距离: 338.0446
  内存使用: 9.9126
  能量使用: 10.1438
  推理时间: 2.4518秒

批次 46:
  奖励值: 700.9318
  收益率: 0.9999
  距离: 332.7477
  内存使用: 9.8131
  能量使用: 10.1775
  推理时间: 2.4327秒

批次 47:
  奖励值: 731.5634
  收益率: 0.9999
  距离: 338.2179
  内存使用: 9.6253
  能量使用: 10.0301
  推理时间: 2.4666秒

批次 48:
  奖励值: 725.9165
  收益率: 0.9999
  距离: 340.6306
  内存使用: 9.7887
  能量使用: 9.9465
  推理时间: 2.4842秒

批次 49:
  奖励值: 717.1375
  收益率: 0.9999
  距离: 329.8315
  内存使用: 9.6668
  能量使用: 10.0261
  推理时间: 2.4086秒

批次 50:
  奖励值: 728.1609
  收益率: 0.9999
  距离: 336.1724
  内存使用: 9.7167
  能量使用: 9.9895
  推理时间: 2.3987秒

批次 51:
  奖励值: 716.6641
  收益率: 0.9999
  距离: 328.1612
  内存使用: 9.8074
  能量使用: 9.9034
  推理时间: 2.4489秒

批次 52:
  奖励值: 740.1592
  收益率: 0.9999
  距离: 329.4200
  内存使用: 9.7522
  能量使用: 9.9089
  推理时间: 2.4453秒

批次 53:
  奖励值: 732.8250
  收益率: 0.9999
  距离: 321.5167
  内存使用: 9.8553
  能量使用: 9.9777
  推理时间: 2.3457秒

批次 54:
  奖励值: 722.5573
  收益率: 0.9999
  距离: 323.6736
  内存使用: 9.4313
  能量使用: 10.3805
  推理时间: 2.4569秒

批次 55:
  奖励值: 721.1838
  收益率: 0.9999
  距离: 334.2366
  内存使用: 9.7356
  能量使用: 10.0603
  推理时间: 2.5162秒

