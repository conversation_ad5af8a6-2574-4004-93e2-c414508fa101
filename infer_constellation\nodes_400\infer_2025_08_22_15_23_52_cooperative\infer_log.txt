推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 152.2907
  收益率: 0.9995
  距离: 65.9137
  内存使用: 1.7332
  能量使用: 1.9716
  推理时间: 0.5528秒

批次 2:
  奖励值: 145.1029
  收益率: 0.9994
  距离: 66.9589
  内存使用: 1.6640
  能量使用: 1.9713
  推理时间: 0.5690秒

批次 3:
  奖励值: 138.5711
  收益率: 0.9994
  距离: 65.3226
  内存使用: 1.7705
  能量使用: 1.9648
  推理时间: 0.4588秒

批次 4:
  奖励值: 146.4187
  收益率: 0.9994
  距离: 63.5113
  内存使用: 1.6870
  能量使用: 1.9643
  推理时间: 0.4763秒

批次 5:
  奖励值: 147.6998
  收益率: 0.9994
  距离: 63.6713
  内存使用: 1.6983
  能量使用: 2.0306
  推理时间: 0.5020秒

批次 6:
  奖励值: 156.1748
  收益率: 0.9989
  距离: 63.7804
  内存使用: 1.6637
  能量使用: 2.0063
  推理时间: 0.4614秒

批次 7:
  奖励值: 142.9442
  收益率: 0.9994
  距离: 67.0727
  内存使用: 1.7347
  能量使用: 1.9042
  推理时间: 0.4919秒

批次 8:
  奖励值: 150.5577
  收益率: 0.9995
  距离: 65.7547
  内存使用: 1.7337
  能量使用: 1.9961
  推理时间: 0.4620秒

批次 9:
  奖励值: 143.6723
  收益率: 0.9994
  距离: 67.6615
  内存使用: 1.8016
  能量使用: 1.9921
  推理时间: 0.5764秒

批次 10:
  奖励值: 147.3277
  收益率: 0.9989
  距离: 67.0609
  内存使用: 1.7366
  能量使用: 1.9471
  推理时间: 0.4537秒

批次 11:
  奖励值: 152.0151
  收益率: 0.9995
  距离: 70.6140
  内存使用: 1.7887
  能量使用: 1.9671
  推理时间: 0.5632秒

批次 12:
  奖励值: 148.4849
  收益率: 0.9995
  距离: 65.4298
  内存使用: 1.5926
  能量使用: 2.0077
  推理时间: 0.5735秒

批次 13:
  奖励值: 145.3531
  收益率: 0.9994
  距离: 68.4450
  内存使用: 1.7419
  能量使用: 1.9067
  推理时间: 0.4544秒

批次 14:
  奖励值: 148.8591
  收益率: 0.9995
  距离: 63.6157
  内存使用: 1.6151
  能量使用: 2.0509
  推理时间: 0.5250秒

批次 15:
  奖励值: 143.0299
  收益率: 0.9989
  距离: 68.0439
  内存使用: 1.7561
  能量使用: 2.1402
  推理时间: 0.4554秒

批次 16:
  奖励值: 138.2172
  收益率: 0.9994
  距离: 67.8835
  内存使用: 1.6645
  能量使用: 2.0177
  推理时间: 0.4594秒

批次 17:
  奖励值: 151.4908
  收益率: 0.9989
  距离: 63.0902
  内存使用: 1.6215
  能量使用: 1.9065
  推理时间: 0.5576秒

批次 18:
  奖励值: 135.7713
  收益率: 0.9994
  距离: 70.3555
  内存使用: 1.6779
  能量使用: 2.0238
  推理时间: 0.6666秒

批次 19:
  奖励值: 148.9822
  收益率: 0.9995
  距离: 69.5352
  内存使用: 1.6866
  能量使用: 2.0137
  推理时间: 0.4572秒

批次 20:
  奖励值: 139.0891
  收益率: 0.9994
  距离: 64.0698
  内存使用: 1.7126
  能量使用: 2.0393
  推理时间: 0.4833秒

批次 21:
  奖励值: 144.3977
  收益率: 0.9994
  距离: 63.5735
  内存使用: 1.6731
  能量使用: 1.9580
  推理时间: 0.5780秒

批次 22:
  奖励值: 149.9042
  收益率: 0.9995
  距离: 65.2874
  内存使用: 1.6651
  能量使用: 2.0390
  推理时间: 0.5970秒

批次 23:
  奖励值: 152.6101
  收益率: 0.9995
  距离: 63.8297
  内存使用: 1.6540
  能量使用: 2.0960
  推理时间: 0.4632秒

批次 24:
  奖励值: 146.4048
  收益率: 0.9994
  距离: 66.2005
  内存使用: 1.6909
  能量使用: 2.0989
  推理时间: 0.5627秒

批次 25:
  奖励值: 145.7244
  收益率: 0.9994
  距离: 68.0250
  内存使用: 1.5768
  能量使用: 1.9495
  推理时间: 0.5655秒

批次 26:
  奖励值: 147.3970
  收益率: 0.9989
  距离: 63.3938
  内存使用: 1.6570
  能量使用: 1.9552
  推理时间: 0.4623秒

批次 27:
  奖励值: 153.3237
  收益率: 0.9995
  距离: 63.6873
  内存使用: 1.6801
  能量使用: 1.9851
  推理时间: 0.4982秒

批次 28:
  奖励值: 138.2242
  收益率: 0.9989
  距离: 69.3967
  内存使用: 1.7038
  能量使用: 2.0508
  推理时间: 0.4648秒

批次 29:
  奖励值: 144.7068
  收益率: 0.9989
  距离: 65.0033
  内存使用: 1.6066
  能量使用: 1.9764
  推理时间: 0.4520秒

批次 30:
  奖励值: 156.9536
  收益率: 0.9995
  距离: 59.9223
  内存使用: 1.6861
  能量使用: 2.0842
  推理时间: 0.4854秒

批次 31:
  奖励值: 145.5585
  收益率: 0.9994
  距离: 65.2068
  内存使用: 1.7193
  能量使用: 1.9569
  推理时间: 0.4929秒

批次 32:
  奖励值: 151.5188
  收益率: 0.9995
  距离: 62.8568
  内存使用: 1.7248
  能量使用: 1.9808
  推理时间: 0.4569秒

批次 33:
  奖励值: 142.9788
  收益率: 0.9994
  距离: 68.6796
  内存使用: 1.5838
  能量使用: 1.9789
  推理时间: 0.4792秒

批次 34:
  奖励值: 146.7860
  收益率: 0.9994
  距离: 64.4907
  内存使用: 1.8216
  能量使用: 1.9157
  推理时间: 0.5013秒

批次 35:
  奖励值: 149.2599
  收益率: 0.9995
  距离: 63.8123
  内存使用: 1.7379
  能量使用: 1.9299
  推理时间: 0.4581秒

批次 36:
  奖励值: 148.9813
  收益率: 0.9994
  距离: 59.7218
  内存使用: 1.6940
  能量使用: 2.0216
  推理时间: 0.5025秒

批次 37:
  奖励值: 139.4424
  收益率: 0.9994
  距离: 66.6896
  内存使用: 1.6552
  能量使用: 1.9703
  推理时间: 0.4557秒

批次 38:
  奖励值: 147.6527
  收益率: 0.9995
  距离: 65.7991
  内存使用: 1.6645
  能量使用: 2.0309
  推理时间: 0.4660秒

批次 39:
  奖励值: 143.9283
  收益率: 0.9994
  距离: 64.2372
  内存使用: 1.6696
  能量使用: 2.0364
  推理时间: 0.4607秒

批次 40:
  奖励值: 141.8435
  收益率: 0.9994
  距离: 66.3590
  内存使用: 1.7487
  能量使用: 2.0053
  推理时间: 0.4512秒

批次 41:
  奖励值: 140.2970
  收益率: 0.9994
  距离: 69.1252
  内存使用: 1.5954
  能量使用: 2.0853
  推理时间: 0.5320秒

批次 42:
  奖励值: 153.6690
  收益率: 0.9995
  距离: 66.5856
  内存使用: 1.7391
  能量使用: 1.9374
  推理时间: 0.5379秒

批次 43:
  奖励值: 151.0826
  收益率: 0.9995
  距离: 69.4075
  内存使用: 1.7074
  能量使用: 1.9199
  推理时间: 0.4603秒

批次 44:
  奖励值: 144.3644
  收益率: 0.9994
  距离: 66.4755
  内存使用: 1.7625
  能量使用: 2.0332
  推理时间: 0.5016秒

批次 45:
  奖励值: 145.7578
  收益率: 0.9994
  距离: 63.1265
  内存使用: 1.6533
  能量使用: 2.1045
  推理时间: 0.4522秒

批次 46:
  奖励值: 146.5506
  收益率: 0.9995
  距离: 69.3901
  内存使用: 1.6631
  能量使用: 2.0456
  推理时间: 0.5015秒

批次 47:
  奖励值: 146.0494
  收益率: 0.9994
  距离: 63.7537
  内存使用: 1.7372
  能量使用: 2.0104
  推理时间: 0.4531秒

批次 48:
  奖励值: 147.4793
  收益率: 0.9995
  距离: 69.6660
  内存使用: 1.6390
  能量使用: 1.9364
  推理时间: 0.5745秒

批次 49:
  奖励值: 140.7051
  收益率: 0.9994
  距离: 66.4181
  内存使用: 1.7145
  能量使用: 1.8571
  推理时间: 0.4543秒

批次 50:
  奖励值: 152.6067
  收益率: 0.9995
  距离: 63.2316
  内存使用: 1.7842
  能量使用: 1.9708
  推理时间: 0.5639秒

批次 51:
  奖励值: 147.5116
  收益率: 0.9994
  距离: 64.3905
  内存使用: 1.6046
  能量使用: 1.9815
  推理时间: 0.4544秒

批次 52:
  奖励值: 137.0024
  收益率: 0.9994
  距离: 72.4374
  内存使用: 1.5937
  能量使用: 1.9640
  推理时间: 0.4672秒

批次 53:
  奖励值: 139.5522
  收益率: 0.9994
  距离: 69.0713
  内存使用: 1.6884
  能量使用: 1.9359
  推理时间: 0.4582秒

批次 54:
  奖励值: 150.8844
  收益率: 0.9995
  距离: 65.8479
  内存使用: 1.8049
  能量使用: 1.9784
  推理时间: 0.5006秒

批次 55:
  奖励值: 152.7512
  收益率: 0.9995
  距离: 70.0067
  内存使用: 1.6869
  能量使用: 2.0040
  推理时间: 0.4588秒

批次 56:
  奖励值: 143.0472
  收益率: 0.9994
  距离: 67.9941
  内存使用: 1.6951
  能量使用: 2.0164
  推理时间: 0.4767秒

批次 57:
  奖励值: 148.7471
  收益率: 0.9989
  距离: 62.0854
  内存使用: 1.6788
  能量使用: 1.9416
  推理时间: 0.6344秒

批次 58:
  奖励值: 141.3540
  收益率: 0.9994
  距离: 64.6977
  内存使用: 1.7361
  能量使用: 2.0583
  推理时间: 0.4520秒

批次 59:
  奖励值: 140.5436
  收益率: 0.9994
  距离: 71.4375
  内存使用: 1.6752
  能量使用: 2.0002
  推理时间: 0.4967秒

批次 60:
  奖励值: 146.1894
  收益率: 0.9994
  距离: 65.9708
  内存使用: 1.5829
  能量使用: 2.0676
  推理时间: 0.4609秒

批次 61:
  奖励值: 148.7723
  收益率: 0.9995
  距离: 66.9218
  内存使用: 1.7528
  能量使用: 1.9806
  推理时间: 0.5024秒

批次 62:
  奖励值: 148.9841
  收益率: 0.9995
  距离: 69.7276
  内存使用: 1.8374
  能量使用: 2.0666
  推理时间: 0.4603秒

批次 63:
  奖励值: 154.2630
  收益率: 0.9995
  距离: 64.7925
  内存使用: 1.7377
  能量使用: 1.9437
  推理时间: 0.4680秒

批次 64:
  奖励值: 138.8039
  收益率: 0.9994
  距离: 69.4396
  内存使用: 1.7055
  能量使用: 2.0469
  推理时间: 0.5019秒

批次 65:
  奖励值: 145.1977
  收益率: 0.9994
  距离: 63.9736
  内存使用: 1.6418
  能量使用: 1.9893
  推理时间: 0.4549秒

批次 66:
  奖励值: 151.8135
  收益率: 0.9995
  距离: 66.7319
  内存使用: 1.6643
  能量使用: 1.9768
  推理时间: 0.4957秒

批次 67:
  奖励值: 156.6246
  收益率: 0.9995
  距离: 66.2683
  内存使用: 1.6252
  能量使用: 2.0570
  推理时间: 0.4793秒

批次 68:
  奖励值: 150.4050
  收益率: 0.9995
  距离: 68.9123
  内存使用: 1.6834
  能量使用: 1.9942
  推理时间: 0.5847秒

批次 69:
  奖励值: 138.2993
  收益率: 0.9994
  距离: 70.1983
  内存使用: 1.6926
  能量使用: 2.1105
  推理时间: 0.4575秒

批次 70:
  奖励值: 143.7098
  收益率: 0.9989
  距离: 67.0508
  内存使用: 1.6130
  能量使用: 1.9166
  推理时间: 0.4811秒

批次 71:
  奖励值: 150.5871
  收益率: 0.9995
  距离: 63.5701
  内存使用: 1.7277
  能量使用: 1.9278
  推理时间: 0.4992秒

批次 72:
  奖励值: 155.9535
  收益率: 0.9995
  距离: 67.4635
  内存使用: 1.7745
  能量使用: 2.0549
  推理时间: 0.4772秒

批次 73:
  奖励值: 148.0047
  收益率: 0.9995
  距离: 70.0421
  内存使用: 1.5964
  能量使用: 1.9519
  推理时间: 0.4886秒

批次 74:
  奖励值: 137.3411
  收益率: 0.9994
  距离: 69.2673
  内存使用: 1.6534
  能量使用: 1.9971
  推理时间: 0.4687秒

批次 75:
  奖励值: 153.3114
  收益率: 0.9995
  距离: 63.5250
  内存使用: 1.7051
  能量使用: 1.9472
  推理时间: 0.5590秒

批次 76:
  奖励值: 147.6903
  收益率: 0.9995
  距离: 68.5431
  内存使用: 1.6585
  能量使用: 2.0179
  推理时间: 0.5670秒

批次 77:
  奖励值: 149.5163
  收益率: 0.9995
  距离: 65.9741
  内存使用: 1.5952
  能量使用: 1.9979
  推理时间: 0.4974秒

批次 78:
  奖励值: 145.6074
  收益率: 0.9994
  距离: 67.6197
  内存使用: 1.6035
  能量使用: 1.9620
  推理时间: 0.5380秒

批次 79:
  奖励值: 150.1800
  收益率: 0.9995
  距离: 67.5502
  内存使用: 1.7115
  能量使用: 1.9783
  推理时间: 0.5420秒

批次 80:
  奖励值: 145.5511
  收益率: 0.9989
  距离: 66.5283
  内存使用: 1.7507
  能量使用: 2.0187
  推理时间: 0.4626秒

批次 81:
  奖励值: 148.1803
  收益率: 0.9995
  距离: 65.2958
  内存使用: 1.7773
  能量使用: 1.9661
  推理时间: 0.4987秒

批次 82:
  奖励值: 143.7285
  收益率: 0.9994
  距离: 65.2640
  内存使用: 1.7306
  能量使用: 1.9483
  推理时间: 0.4581秒

批次 83:
  奖励值: 147.1286
  收益率: 0.9995
  距离: 67.9523
  内存使用: 1.6028
  能量使用: 1.9875
  推理时间: 0.4928秒

批次 84:
  奖励值: 142.1613
  收益率: 0.9994
  距离: 64.1036
  内存使用: 1.6367
  能量使用: 1.9372
  推理时间: 0.5022秒

批次 85:
  奖励值: 141.7543
  收益率: 0.9989
  距离: 75.8366
  内存使用: 1.6735
  能量使用: 1.9814
  推理时间: 0.4720秒

批次 86:
  奖励值: 142.0068
  收益率: 0.9994
  距离: 68.1591
  内存使用: 1.6105
  能量使用: 2.0167
  推理时间: 0.4814秒

批次 87:
  奖励值: 148.6243
  收益率: 0.9995
  距离: 65.4253
  内存使用: 1.7452
  能量使用: 1.9810
  推理时间: 0.4581秒

批次 88:
  奖励值: 148.9659
  收益率: 0.9989
  距离: 64.8136
  内存使用: 1.6588
  能量使用: 1.9956
  推理时间: 0.5505秒

批次 89:
  奖励值: 150.8037
  收益率: 0.9995
  距离: 64.4628
  内存使用: 1.6474
  能量使用: 1.8823
  推理时间: 0.4515秒

批次 90:
  奖励值: 150.5312
  收益率: 0.9995
  距离: 61.9465
  内存使用: 1.6780
  能量使用: 1.9131
  推理时间: 0.4748秒

批次 91:
  奖励值: 138.6375
  收益率: 0.9994
  距离: 72.6239
  内存使用: 1.7733
  能量使用: 2.1278
  推理时间: 0.5772秒

批次 92:
  奖励值: 142.5195
  收益率: 0.9989
  距离: 65.0680
  内存使用: 1.6998
  能量使用: 1.9932
  推理时间: 0.4603秒

批次 93:
  奖励值: 147.5822
  收益率: 0.9995
  距离: 65.7401
  内存使用: 1.5899
  能量使用: 1.9055
  推理时间: 0.4824秒

批次 94:
  奖励值: 153.1729
  收益率: 0.9995
  距离: 65.0702
  内存使用: 1.6654
  能量使用: 2.1184
  推理时间: 0.5057秒

批次 95:
  奖励值: 149.3165
  收益率: 0.9995
  距离: 67.3203
  内存使用: 1.6661
  能量使用: 1.9807
  推理时间: 0.4512秒

批次 96:
  奖励值: 144.6055
  收益率: 0.9994
  距离: 64.9489
  内存使用: 1.6857
  能量使用: 1.9544
  推理时间: 0.6810秒

批次 97:
  奖励值: 153.4227
  收益率: 0.9984
  距离: 60.9187
  内存使用: 1.6835
  能量使用: 1.9525
  推理时间: 0.5793秒

批次 98:
  奖励值: 144.9992
  收益率: 0.9994
  距离: 67.0343
  内存使用: 1.7225
  能量使用: 2.0448
  推理时间: 0.4972秒

批次 99:
  奖励值: 140.0022
  收益率: 0.9994
  距离: 66.6188
  内存使用: 1.7887
  能量使用: 1.9880
  推理时间: 0.4655秒

批次 100:
  奖励值: 149.1792
  收益率: 0.9995
  距离: 65.1930
  内存使用: 1.7623
  能量使用: 2.0862
  推理时间: 0.4648秒


==================== 总结 ====================
平均收益率: 0.9994
平均能量使用: 1.9939
平均推理时间: 0.4993秒
