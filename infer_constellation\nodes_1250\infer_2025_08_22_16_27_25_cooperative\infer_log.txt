推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 439.1755
  收益率: 0.9998
  距离: 212.8839
  内存使用: 5.7096
  能量使用: 6.2559
  推理时间: 1.5470秒

批次 2:
  奖励值: 461.1137
  收益率: 0.9998
  距离: 203.5196
  内存使用: 5.8728
  能量使用: 6.3815
  推理时间: 1.4814秒

批次 3:
  奖励值: 456.9532
  收益率: 0.9998
  距离: 207.8870
  内存使用: 5.7774
  能量使用: 6.4298
  推理时间: 1.4383秒

批次 4:
  奖励值: 456.6773
  收益率: 0.9998
  距离: 205.7469
  内存使用: 6.0021
  能量使用: 6.2968
  推理时间: 1.4315秒

批次 5:
  奖励值: 460.6396
  收益率: 0.9998
  距离: 211.6106
  内存使用: 5.9767
  能量使用: 6.1344
  推理时间: 1.4510秒

批次 6:
  奖励值: 458.2978
  收益率: 0.9998
  距离: 214.7007
  内存使用: 6.0000
  能量使用: 6.1038
  推理时间: 1.4454秒

批次 7:
  奖励值: 451.6507
  收益率: 0.9998
  距离: 212.5046
  内存使用: 5.8633
  能量使用: 6.3311
  推理时间: 1.4553秒

批次 8:
  奖励值: 453.2133
  收益率: 0.9998
  距离: 212.0422
  内存使用: 5.8323
  能量使用: 6.0994
  推理时间: 1.5157秒

批次 9:
  奖励值: 458.4982
  收益率: 0.9998
  距离: 203.7248
  内存使用: 5.8311
  能量使用: 6.2484
  推理时间: 1.6424秒

批次 10:
  奖励值: 457.9473
  收益率: 0.9998
  距离: 203.7173
  内存使用: 5.8585
  能量使用: 6.3304
  推理时间: 1.4520秒

批次 11:
  奖励值: 449.2614
  收益率: 0.9998
  距离: 211.9362
  内存使用: 5.8795
  能量使用: 6.2618
  推理时间: 1.4487秒

批次 12:
  奖励值: 467.8184
  收益率: 0.9998
  距离: 209.8008
  内存使用: 6.0618
  能量使用: 6.1006
  推理时间: 1.4595秒

批次 13:
  奖励值: 447.8338
  收益率: 0.9998
  距离: 210.3478
  内存使用: 5.9360
  能量使用: 6.2490
  推理时间: 1.4494秒

批次 14:
  奖励值: 458.8946
  收益率: 0.9998
  距离: 212.6832
  内存使用: 5.9704
  能量使用: 6.1580
  推理时间: 1.4532秒

批次 15:
  奖励值: 455.6830
  收益率: 0.9998
  距离: 206.4957
  内存使用: 5.8885
  能量使用: 6.2502
  推理时间: 1.4514秒

批次 16:
  奖励值: 451.2013
  收益率: 0.9998
  距离: 206.0157
  内存使用: 6.0132
  能量使用: 6.1691
  推理时间: 1.4575秒

批次 17:
  奖励值: 447.8392
  收益率: 0.9998
  距离: 205.6805
  内存使用: 5.8306
  能量使用: 6.2106
  推理时间: 1.4387秒

批次 18:
  奖励值: 464.3496
  收益率: 0.9998
  距离: 202.8640
  内存使用: 6.0426
  能量使用: 6.3947
  推理时间: 1.4233秒

批次 19:
  奖励值: 474.9630
  收益率: 0.9998
  距离: 207.6081
  内存使用: 5.9901
  能量使用: 6.0759
  推理时间: 1.4314秒

批次 20:
  奖励值: 457.2967
  收益率: 0.9998
  距离: 215.0397
  内存使用: 5.8871
  能量使用: 6.2800
  推理时间: 1.4810秒

批次 21:
  奖励值: 455.1457
  收益率: 0.9998
  距离: 204.0429
  内存使用: 6.0128
  能量使用: 6.2537
  推理时间: 1.4573秒

批次 22:
  奖励值: 465.4561
  收益率: 0.9998
  距离: 209.2296
  内存使用: 5.9660
  能量使用: 6.2932
  推理时间: 1.4372秒

批次 23:
  奖励值: 452.1438
  收益率: 0.9998
  距离: 207.3104
  内存使用: 5.9982
  能量使用: 6.2044
  推理时间: 1.5493秒

批次 24:
  奖励值: 459.8440
  收益率: 0.9998
  距离: 205.0264
  内存使用: 5.8734
  能量使用: 6.4128
  推理时间: 1.4436秒

批次 25:
  奖励值: 452.0978
  收益率: 0.9998
  距离: 209.8352
  内存使用: 5.9784
  能量使用: 5.9917
  推理时间: 1.4546秒

批次 26:
  奖励值: 452.6052
  收益率: 0.9998
  距离: 208.3910
  内存使用: 5.9297
  能量使用: 6.2695
  推理时间: 1.4623秒

批次 27:
  奖励值: 452.6166
  收益率: 0.9998
  距离: 208.9991
  内存使用: 6.0244
  能量使用: 6.3433
  推理时间: 1.4954秒

批次 28:
  奖励值: 469.2774
  收益率: 0.9998
  距离: 208.9908
  内存使用: 6.0593
  能量使用: 6.1954
  推理时间: 1.4511秒

批次 29:
  奖励值: 458.8380
  收益率: 0.9998
  距离: 203.5136
  内存使用: 5.8563
  能量使用: 6.1542
  推理时间: 1.4473秒

批次 30:
  奖励值: 460.4294
  收益率: 0.9998
  距离: 203.7052
  内存使用: 5.9604
  能量使用: 6.2753
  推理时间: 1.4506秒

批次 31:
  奖励值: 439.2969
  收益率: 0.9998
  距离: 216.9134
  内存使用: 5.9634
  能量使用: 6.1299
  推理时间: 1.4563秒

批次 32:
  奖励值: 458.9457
  收益率: 0.9998
  距离: 205.5507
  内存使用: 6.0425
  能量使用: 6.3161
  推理时间: 1.4399秒

批次 33:
  奖励值: 454.4951
  收益率: 0.9998
  距离: 217.6945
  内存使用: 5.9708
  能量使用: 6.3449
  推理时间: 1.5053秒

批次 34:
  奖励值: 446.4780
  收益率: 0.9998
  距离: 213.8501
  内存使用: 5.9594
  能量使用: 6.2350
  推理时间: 1.4312秒

批次 35:
  奖励值: 453.9028
  收益率: 0.9998
  距离: 211.5905
  内存使用: 5.9635
  能量使用: 6.2411
  推理时间: 1.4256秒

批次 36:
  奖励值: 447.3102
  收益率: 0.9998
  距离: 203.3433
  内存使用: 5.9481
  能量使用: 6.2886
  推理时间: 1.4437秒

批次 37:
  奖励值: 458.0060
  收益率: 0.9996
  距离: 201.3418
  内存使用: 6.0255
  能量使用: 6.2214
  推理时间: 1.5100秒

批次 38:
  奖励值: 462.5530
  收益率: 0.9998
  距离: 211.5006
  内存使用: 5.8157
  能量使用: 6.1783
  推理时间: 1.5637秒

批次 39:
  奖励值: 454.1281
  收益率: 0.9998
  距离: 211.1777
  内存使用: 5.8481
  能量使用: 6.1186
  推理时间: 1.4528秒

批次 40:
  奖励值: 452.9851
  收益率: 0.9998
  距离: 207.6125
  内存使用: 5.9076
  能量使用: 6.3102
  推理时间: 1.5572秒

批次 41:
  奖励值: 452.4381
  收益率: 0.9998
  距离: 209.2375
  内存使用: 5.9302
  能量使用: 6.3563
  推理时间: 1.5038秒

批次 42:
  奖励值: 460.5899
  收益率: 0.9998
  距离: 212.9522
  内存使用: 6.0430
  能量使用: 6.4258
  推理时间: 1.6093秒

批次 43:
  奖励值: 451.8855
  收益率: 0.9998
  距离: 207.9029
  内存使用: 6.0890
  能量使用: 6.2380
  推理时间: 1.4479秒

批次 44:
  奖励值: 458.2345
  收益率: 0.9998
  距离: 201.2764
  内存使用: 5.9357
  能量使用: 6.1194
  推理时间: 1.5726秒

批次 45:
  奖励值: 443.8197
  收益率: 0.9998
  距离: 215.3992
  内存使用: 5.8840
  能量使用: 6.2779
  推理时间: 1.4581秒

批次 46:
  奖励值: 455.5490
  收益率: 0.9998
  距离: 198.3898
  内存使用: 5.9378
  能量使用: 6.1754
  推理时间: 1.5815秒

批次 47:
  奖励值: 464.4383
  收益率: 0.9998
  距离: 202.4508
  内存使用: 6.0160
  能量使用: 6.2570
  推理时间: 1.4453秒

批次 48:
  奖励值: 447.8585
  收益率: 0.9998
  距离: 206.4820
  内存使用: 5.8139
  能量使用: 6.3873
  推理时间: 1.4338秒

批次 49:
  奖励值: 452.2997
  收益率: 0.9998
  距离: 207.7747
  内存使用: 5.7557
  能量使用: 6.2708
  推理时间: 1.4436秒

批次 50:
  奖励值: 447.9553
  收益率: 0.9998
  距离: 203.9672
  内存使用: 5.8960
  能量使用: 6.2270
  推理时间: 1.4823秒

批次 51:
  奖励值: 452.4879
  收益率: 0.9998
  距离: 209.8385
  内存使用: 5.6023
  能量使用: 6.3841
  推理时间: 1.4451秒

批次 52:
  奖励值: 453.7320
  收益率: 0.9998
  距离: 200.4895
  内存使用: 5.9317
  能量使用: 6.1146
  推理时间: 1.4463秒

批次 53:
  奖励值: 459.3697
  收益率: 0.9998
  距离: 203.1158
  内存使用: 6.0195
  能量使用: 6.3259
  推理时间: 1.4536秒

批次 54:
  奖励值: 435.1450
  收益率: 0.9998
  距离: 213.9200
  内存使用: 5.9285
  能量使用: 6.0624
  推理时间: 1.5690秒

批次 55:
  奖励值: 456.3878
  收益率: 0.9998
  距离: 207.9501
  内存使用: 5.9212
  能量使用: 5.9541
  推理时间: 1.4369秒

批次 56:
  奖励值: 452.4479
  收益率: 0.9998
  距离: 209.9435
  内存使用: 6.1553
  能量使用: 6.2063
  推理时间: 1.5998秒

批次 57:
  奖励值: 462.6295
  收益率: 0.9998
  距离: 204.6934
  内存使用: 5.8523
  能量使用: 6.3959
  推理时间: 1.4414秒

批次 58:
  奖励值: 437.6610
  收益率: 0.9998
  距离: 223.6659
  内存使用: 5.8658
  能量使用: 6.1468
  推理时间: 1.6145秒

批次 59:
  奖励值: 471.6566
  收益率: 0.9998
  距离: 204.8251
  内存使用: 5.9948
  能量使用: 6.4673
  推理时间: 1.4374秒

批次 60:
  奖励值: 437.4581
  收益率: 0.9998
  距离: 211.5659
  内存使用: 5.9519
  能量使用: 6.3666
  推理时间: 1.4337秒

批次 61:
  奖励值: 467.2387
  收益率: 0.9998
  距离: 216.6621
  内存使用: 5.8206
  能量使用: 6.2410
  推理时间: 1.4751秒

批次 62:
  奖励值: 458.5995
  收益率: 0.9998
  距离: 202.0213
  内存使用: 6.2283
  能量使用: 6.3517
  推理时间: 1.4608秒

批次 63:
  奖励值: 450.4785
  收益率: 0.9998
  距离: 213.6066
  内存使用: 5.9364
  能量使用: 6.3007
  推理时间: 1.4477秒

批次 64:
  奖励值: 463.6135
  收益率: 0.9998
  距离: 208.1407
  内存使用: 5.8766
  能量使用: 6.1561
  推理时间: 1.4557秒

批次 65:
  奖励值: 462.6766
  收益率: 0.9998
  距离: 202.2037
  内存使用: 5.8553
  能量使用: 6.1882
  推理时间: 1.4423秒

批次 66:
  奖励值: 443.9338
  收益率: 0.9998
  距离: 205.7581
  内存使用: 5.8969
  能量使用: 6.2779
  推理时间: 1.4399秒

批次 67:
  奖励值: 457.6919
  收益率: 0.9998
  距离: 210.9275
  内存使用: 5.9757
  能量使用: 6.1138
  推理时间: 1.4474秒

批次 68:
  奖励值: 441.4987
  收益率: 0.9998
  距离: 202.7840
  内存使用: 6.1479
  能量使用: 6.0709
  推理时间: 1.5079秒

批次 69:
  奖励值: 448.4507
  收益率: 0.9998
  距离: 212.4867
  内存使用: 6.1762
  能量使用: 6.2364
  推理时间: 1.6295秒

批次 70:
  奖励值: 472.1994
  收益率: 0.9998
  距离: 204.9761
  内存使用: 5.9511
  能量使用: 6.0741
  推理时间: 1.4416秒

批次 71:
  奖励值: 450.0490
  收益率: 0.9998
  距离: 205.7840
  内存使用: 5.9073
  能量使用: 6.2117
  推理时间: 1.5585秒

批次 72:
  奖励值: 453.1941
  收益率: 0.9998
  距离: 210.8656
  内存使用: 6.0464
  能量使用: 6.3002
  推理时间: 1.4537秒

批次 73:
  奖励值: 462.7985
  收益率: 0.9998
  距离: 205.0768
  内存使用: 6.0394
  能量使用: 6.2883
  推理时间: 1.5921秒

批次 74:
  奖励值: 451.6264
  收益率: 0.9998
  距离: 204.4559
  内存使用: 6.0369
  能量使用: 6.2546
  推理时间: 1.4298秒

批次 75:
  奖励值: 465.1090
  收益率: 0.9998
  距离: 198.7576
  内存使用: 6.0516
  能量使用: 6.3734
  推理时间: 1.5870秒

批次 76:
  奖励值: 456.9932
  收益率: 0.9998
  距离: 197.7144
  内存使用: 5.8582
  能量使用: 6.2413
  推理时间: 1.4346秒

批次 77:
  奖励值: 441.4774
  收益率: 0.9998
  距离: 208.0585
  内存使用: 5.9338
  能量使用: 6.2537
  推理时间: 1.4445秒

批次 78:
  奖励值: 455.9148
  收益率: 0.9998
  距离: 202.3830
  内存使用: 5.9020
  能量使用: 6.2862
  推理时间: 1.4446秒

批次 79:
  奖励值: 460.3118
  收益率: 0.9998
  距离: 209.6167
  内存使用: 6.0214
  能量使用: 6.1395
  推理时间: 1.4405秒

批次 80:
  奖励值: 454.9886
  收益率: 0.9998
  距离: 201.7280
  内存使用: 6.1313
  能量使用: 6.1643
  推理时间: 1.4378秒

批次 81:
  奖励值: 444.1808
  收益率: 0.9998
  距离: 212.4066
  内存使用: 5.8747
  能量使用: 6.3580
  推理时间: 1.4507秒

批次 82:
  奖励值: 468.8791
  收益率: 0.9998
  距离: 205.3882
  内存使用: 5.8715
  能量使用: 6.3829
  推理时间: 1.4504秒

批次 83:
  奖励值: 457.0297
  收益率: 0.9998
  距离: 204.5059
  内存使用: 6.0102
  能量使用: 6.2246
  推理时间: 1.4278秒

批次 84:
  奖励值: 444.0453
  收益率: 0.9998
  距离: 208.9652
  内存使用: 5.8215
  能量使用: 6.3231
  推理时间: 1.4772秒

批次 85:
  奖励值: 447.2661
  收益率: 0.9998
  距离: 207.4699
  内存使用: 5.8214
  能量使用: 6.3767
  推理时间: 1.4205秒

批次 86:
  奖励值: 457.6023
  收益率: 0.9998
  距离: 204.8652
  内存使用: 5.7752
  能量使用: 6.1549
  推理时间: 1.6545秒

批次 87:
  奖励值: 448.0119
  收益率: 0.9998
  距离: 205.5600
  内存使用: 5.8430
  能量使用: 6.3742
  推理时间: 1.4639秒

批次 88:
  奖励值: 436.1436
  收益率: 0.9998
  距离: 212.1614
  内存使用: 6.0233
  能量使用: 6.3293
  推理时间: 1.6647秒

批次 89:
  奖励值: 457.6118
  收益率: 0.9998
  距离: 209.8596
  内存使用: 5.8739
  能量使用: 6.2436
  推理时间: 1.4506秒

批次 90:
  奖励值: 455.8606
  收益率: 0.9998
  距离: 207.8014
  内存使用: 5.9813
  能量使用: 6.4968
  推理时间: 1.5827秒

批次 91:
  奖励值: 467.0562
  收益率: 0.9998
  距离: 205.8677
  内存使用: 5.9183
  能量使用: 6.1022
  推理时间: 1.4499秒

批次 92:
  奖励值: 453.5257
  收益率: 0.9998
  距离: 198.3887
  内存使用: 5.9993
  能量使用: 6.1611
  推理时间: 1.5887秒

批次 93:
  奖励值: 458.0019
  收益率: 0.9998
  距离: 202.5263
  内存使用: 6.1848
  能量使用: 6.2858
  推理时间: 1.4618秒

批次 94:
  奖励值: 456.1001
  收益率: 0.9998
  距离: 202.8538
  内存使用: 5.9530
  能量使用: 6.1932
  推理时间: 1.4576秒

批次 95:
  奖励值: 474.5990
  收益率: 0.9998
  距离: 205.7712
  内存使用: 5.9031
  能量使用: 6.3289
  推理时间: 1.4573秒

批次 96:
  奖励值: 462.0865
  收益率: 0.9998
  距离: 212.3583
  内存使用: 5.9158
  能量使用: 6.3534
  推理时间: 1.4467秒

批次 97:
  奖励值: 446.6564
  收益率: 0.9998
  距离: 208.8810
  内存使用: 5.8955
  能量使用: 6.1111
  推理时间: 1.5143秒

批次 98:
  奖励值: 470.2037
  收益率: 0.9998
  距离: 196.1649
  内存使用: 5.9800
  能量使用: 6.2482
  推理时间: 1.4440秒

批次 99:
  奖励值: 457.4020
  收益率: 0.9998
  距离: 205.0255
  内存使用: 5.8524
  能量使用: 6.3186
  推理时间: 1.4642秒

批次 100:
  奖励值: 458.8707
  收益率: 0.9998
  距离: 204.0796
  内存使用: 5.8235
  能量使用: 6.3560
  推理时间: 1.6388秒


==================== 总结 ====================
平均收益率: 0.9998
平均能量使用: 6.2494
平均推理时间: 1.4818秒
