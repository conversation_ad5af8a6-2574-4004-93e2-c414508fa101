多星座模式训练详细日志
================================================================================

实验时间: 2025-08-22 13:42:59
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练1000, 验证100
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
训练结果:
  最佳验证奖励: 25.957942
测试性能:
  平均收益率: 0.738654
  平均距离: 14.515403
  平均内存使用: 0.131844
  平均功耗: 0.358798
  综合性能评分: -0.116480
文件路径:
  模型保存: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
训练结果:
  最佳验证奖励: 26.072676
测试性能:
  平均收益率: 0.736765
  平均距离: 14.322880
  平均内存使用: 0.141273
  平均功耗: 0.359795
  综合性能评分: -0.044321
文件路径:
  模型保存: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_competitive_transformer_L2H4_2025_08_22_13_38_06
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 4,059,400
  Critic参数数量: 691,149
  总参数数量: 4,750,549
训练结果:
  最佳验证奖励: 26.682175
测试性能:
  平均收益率: 0.736205
  平均距离: 14.764004
  平均内存使用: 0.128922
  平均功耗: 0.357928
  综合性能评分: -0.263374
文件路径:
  模型保存: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_hybrid_transformer_L2H4_2025_08_22_13_40_28
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: hybrid (26.6822)
最佳收益率模式: cooperative (0.7387)
最短距离模式: competitive (14.3229)
最低功耗模式: hybrid (0.3579)

推荐使用: hybrid 模式
推荐理由: 在关键性能指标上表现最佳
