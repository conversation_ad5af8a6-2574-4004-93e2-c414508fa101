推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 352.0692
  收益率: 0.9998
  距离: 163.4120
  内存使用: 4.7825
  能量使用: 5.0674
  推理时间: 1.3393秒

批次 2:
  奖励值: 365.1879
  收益率: 0.9998
  距离: 168.5769
  内存使用: 4.8541
  能量使用: 4.9936
  推理时间: 1.1738秒

批次 3:
  奖励值: 374.9761
  收益率: 0.9998
  距离: 156.6238
  内存使用: 4.6315
  能量使用: 4.9924
  推理时间: 1.2502秒

批次 4:
  奖励值: 365.9771
  收益率: 0.9998
  距离: 171.9447
  内存使用: 4.6871
  能量使用: 5.0150
  推理时间: 1.1149秒

批次 5:
  奖励值: 359.2059
  收益率: 0.9998
  距离: 171.9864
  内存使用: 4.6879
  能量使用: 4.9143
  推理时间: 1.2910秒

批次 6:
  奖励值: 364.9043
  收益率: 0.9998
  距离: 159.9067
  内存使用: 4.6806
  能量使用: 4.8048
  推理时间: 1.1360秒

批次 7:
  奖励值: 355.8356
  收益率: 0.9998
  距离: 165.8079
  内存使用: 4.7895
  能量使用: 4.9317
  推理时间: 1.1201秒

批次 8:
  奖励值: 359.3496
  收益率: 0.9998
  距离: 168.2971
  内存使用: 4.8274
  能量使用: 4.9766
  推理时间: 1.2907秒

批次 9:
  奖励值: 364.6007
  收益率: 0.9998
  距离: 161.9856
  内存使用: 4.7436
  能量使用: 4.8693
  推理时间: 1.1251秒

批次 10:
  奖励值: 369.8065
  收益率: 0.9998
  距离: 164.9690
  内存使用: 4.6454
  能量使用: 4.9728
  推理时间: 1.1665秒

批次 11:
  奖励值: 378.5088
  收益率: 0.9998
  距离: 158.9202
  内存使用: 4.6352
  能量使用: 4.8275
  推理时间: 1.2549秒

批次 12:
  奖励值: 359.2581
  收益率: 0.9998
  距离: 161.9051
  内存使用: 4.6345
  能量使用: 4.9446
  推理时间: 1.1201秒

批次 13:
  奖励值: 359.9460
  收益率: 0.9998
  距离: 170.5495
  内存使用: 4.6668
  能量使用: 4.8920
  推理时间: 1.2412秒

批次 14:
  奖励值: 361.9473
  收益率: 0.9998
  距离: 160.9172
  内存使用: 4.8370
  能量使用: 4.7516
  推理时间: 1.1287秒

批次 15:
  奖励值: 356.9990
  收益率: 0.9998
  距离: 168.2167
  内存使用: 4.7755
  能量使用: 5.0103
  推理时间: 1.1291秒

批次 16:
  奖励值: 360.4461
  收益率: 0.9998
  距离: 168.6188
  内存使用: 4.6317
  能量使用: 4.8576
  推理时间: 1.1283秒

批次 17:
  奖励值: 360.0790
  收益率: 0.9998
  距离: 169.2262
  内存使用: 4.4706
  能量使用: 4.9456
  推理时间: 1.2422秒

批次 18:
  奖励值: 355.7372
  收益率: 0.9998
  距离: 168.3066
  内存使用: 4.5275
  能量使用: 5.0919
  推理时间: 1.1185秒

批次 19:
  奖励值: 363.6419
  收益率: 0.9996
  距离: 165.4579
  内存使用: 4.6072
  能量使用: 5.0518
  推理时间: 1.1515秒

批次 20:
  奖励值: 370.0909
  收益率: 0.9998
  距离: 164.3685
  内存使用: 4.6553
  能量使用: 4.9950
  推理时间: 1.2454秒

批次 21:
  奖励值: 383.3394
  收益率: 0.9998
  距离: 168.6339
  内存使用: 4.6872
  能量使用: 5.0009
  推理时间: 1.1252秒

批次 22:
  奖励值: 367.4499
  收益率: 0.9998
  距离: 166.3639
  内存使用: 4.7497
  能量使用: 4.7868
  推理时间: 1.2962秒

批次 23:
  奖励值: 361.2352
  收益率: 0.9998
  距离: 160.9141
  内存使用: 4.6879
  能量使用: 4.9280
  推理时间: 1.1551秒

批次 24:
  奖励值: 364.4131
  收益率: 0.9998
  距离: 171.6235
  内存使用: 4.6576
  能量使用: 4.8929
  推理时间: 1.1413秒

批次 25:
  奖励值: 383.7021
  收益率: 0.9998
  距离: 158.2585
  内存使用: 4.5773
  能量使用: 4.9606
  推理时间: 1.3137秒

批次 26:
  奖励值: 356.4253
  收益率: 0.9998
  距离: 172.3609
  内存使用: 4.6179
  能量使用: 4.9710
  推理时间: 1.1417秒

批次 27:
  奖励值: 356.6460
  收益率: 0.9998
  距离: 162.5739
  内存使用: 4.7003
  能量使用: 5.0343
  推理时间: 1.1318秒

批次 28:
  奖励值: 364.2036
  收益率: 0.9998
  距离: 167.5728
  内存使用: 4.6423
  能量使用: 4.9778
  推理时间: 1.2945秒

批次 29:
  奖励值: 365.2907
  收益率: 0.9998
  距离: 165.1417
  内存使用: 4.6360
  能量使用: 5.0412
  推理时间: 1.1287秒

批次 30:
  奖励值: 376.4445
  收益率: 0.9998
  距离: 168.8976
  内存使用: 4.6074
  能量使用: 5.0065
  推理时间: 1.1136秒

批次 31:
  奖励值: 358.2264
  收益率: 0.9998
  距离: 171.2013
  内存使用: 4.5502
  能量使用: 4.9959
  推理时间: 1.1356秒

批次 32:
  奖励值: 365.6841
  收益率: 0.9996
  距离: 165.2594
  内存使用: 4.6063
  能量使用: 4.9670
  推理时间: 1.1617秒

批次 33:
  奖励值: 367.0010
  收益率: 0.9998
  距离: 165.1872
  内存使用: 4.6673
  能量使用: 4.9441
  推理时间: 1.2478秒

批次 34:
  奖励值: 364.0193
  收益率: 0.9998
  距离: 174.2269
  内存使用: 4.7367
  能量使用: 4.9980
  推理时间: 1.1229秒

批次 35:
  奖励值: 361.7494
  收益率: 0.9998
  距离: 169.3762
  内存使用: 4.6505
  能量使用: 5.0751
  推理时间: 1.1264秒

批次 36:
  奖励值: 365.1872
  收益率: 0.9996
  距离: 161.2316
  内存使用: 4.4821
  能量使用: 4.9127
  推理时间: 1.2512秒

批次 37:
  奖励值: 364.5579
  收益率: 0.9998
  距离: 159.3581
  内存使用: 4.6069
  能量使用: 5.1195
  推理时间: 1.1415秒

批次 38:
  奖励值: 363.8238
  收益率: 0.9998
  距离: 157.0966
  内存使用: 4.7950
  能量使用: 4.8617
  推理时间: 1.1222秒

批次 39:
  奖励值: 352.2497
  收益率: 0.9995
  距离: 162.2847
  内存使用: 4.7297
  能量使用: 5.0871
  推理时间: 1.1798秒

批次 40:
  奖励值: 365.6836
  收益率: 0.9998
  距离: 165.5461
  内存使用: 4.6103
  能量使用: 5.0771
  推理时间: 1.3056秒

批次 41:
  奖励值: 364.7747
  收益率: 0.9998
  距离: 162.9123
  内存使用: 4.6385
  能量使用: 5.1001
  推理时间: 1.1446秒

批次 42:
  奖励值: 369.5163
  收益率: 0.9998
  距离: 165.2268
  内存使用: 4.7972
  能量使用: 4.9431
  推理时间: 1.2845秒

批次 43:
  奖励值: 350.6446
  收益率: 0.9998
  距离: 173.1712
  内存使用: 4.6186
  能量使用: 4.9222
  推理时间: 1.1218秒

批次 44:
  奖励值: 356.2544
  收益率: 0.9998
  距离: 167.2083
  内存使用: 4.6662
  能量使用: 5.0172
  推理时间: 1.1276秒

批次 45:
  奖励值: 361.8800
  收益率: 0.9998
  距离: 167.3554
  内存使用: 4.4923
  能量使用: 4.9930
  推理时间: 1.2500秒

批次 46:
  奖励值: 362.1558
  收益率: 0.9998
  距离: 158.6078
  内存使用: 4.6335
  能量使用: 5.0476
  推理时间: 1.1268秒

批次 47:
  奖励值: 366.5932
  收益率: 0.9998
  距离: 163.9602
  内存使用: 4.5577
  能量使用: 5.0963
  推理时间: 1.1358秒

批次 48:
  奖励值: 352.2645
  收益率: 0.9998
  距离: 160.1613
  内存使用: 4.7112
  能量使用: 4.9986
  推理时间: 1.1195秒

批次 49:
  奖励值: 361.6378
  收益率: 0.9998
  距离: 173.6835
  内存使用: 4.7006
  能量使用: 4.9415
  推理时间: 1.2613秒

批次 50:
  奖励值: 370.7607
  收益率: 0.9998
  距离: 173.2322
  内存使用: 4.6265
  能量使用: 5.0204
  推理时间: 1.1256秒

批次 51:
  奖励值: 368.5322
  收益率: 0.9998
  距离: 169.4941
  内存使用: 4.7828
  能量使用: 5.0590
  推理时间: 1.3196秒

批次 52:
  奖励值: 368.0157
  收益率: 0.9998
  距离: 167.1086
  内存使用: 4.6161
  能量使用: 5.0448
  推理时间: 1.1204秒

批次 53:
  奖励值: 349.2476
  收益率: 0.9998
  距离: 168.5811
  内存使用: 4.6136
  能量使用: 4.9109
  推理时间: 1.1268秒

批次 54:
  奖励值: 367.2097
  收益率: 0.9998
  距离: 163.6699
  内存使用: 4.8351
  能量使用: 5.0763
  推理时间: 1.1627秒

批次 55:
  奖励值: 368.6690
  收益率: 0.9998
  距离: 159.1184
  内存使用: 4.7107
  能量使用: 5.0335
  推理时间: 1.1235秒

批次 56:
  奖励值: 366.3228
  收益率: 0.9998
  距离: 169.2847
  内存使用: 4.7697
  能量使用: 4.9003
  推理时间: 1.2393秒

批次 57:
  奖励值: 345.8445
  收益率: 0.9998
  距离: 171.5059
  内存使用: 4.6972
  能量使用: 5.1084
  推理时间: 1.1155秒

批次 58:
  奖励值: 367.3445
  收益率: 0.9998
  距离: 162.4697
  内存使用: 4.6442
  能量使用: 4.9973
  推理时间: 1.2747秒

批次 59:
  奖励值: 362.4232
  收益率: 0.9998
  距离: 162.6536
  内存使用: 4.6626
  能量使用: 5.0381
  推理时间: 1.1197秒

批次 60:
  奖励值: 357.1346
  收益率: 0.9998
  距离: 168.9284
  内存使用: 4.7144
  能量使用: 4.8889
  推理时间: 1.1154秒

批次 61:
  奖励值: 358.1122
  收益率: 0.9998
  距离: 172.6941
  内存使用: 4.6632
  能量使用: 5.0188
  推理时间: 1.2399秒

批次 62:
  奖励值: 362.5413
  收益率: 0.9996
  距离: 165.7566
  内存使用: 4.7316
  能量使用: 4.8301
  推理时间: 1.1127秒

批次 63:
  奖励值: 373.3902
  收益率: 0.9998
  距离: 163.0130
  内存使用: 4.6901
  能量使用: 4.9174
  推理时间: 1.2803秒

批次 64:
  奖励值: 360.1190
  收益率: 0.9996
  距离: 168.0095
  内存使用: 4.6099
  能量使用: 5.1428
  推理时间: 1.1236秒

批次 65:
  奖励值: 365.9171
  收益率: 0.9998
  距离: 165.9904
  内存使用: 4.7728
  能量使用: 5.0035
  推理时间: 1.1205秒

批次 66:
  奖励值: 362.7757
  收益率: 0.9998
  距离: 168.1536
  内存使用: 4.7740
  能量使用: 5.1216
  推理时间: 1.2566秒

批次 67:
  奖励值: 362.9984
  收益率: 0.9998
  距离: 166.4296
  内存使用: 4.7065
  能量使用: 5.0680
  推理时间: 1.1165秒

批次 68:
  奖励值: 360.8735
  收益率: 0.9998
  距离: 166.4731
  内存使用: 4.6280
  能量使用: 4.9526
  推理时间: 1.2377秒

批次 69:
  奖励值: 362.7271
  收益率: 0.9998
  距离: 169.1129
  内存使用: 4.6325
  能量使用: 4.8009
  推理时间: 1.1273秒

批次 70:
  奖励值: 361.4758
  收益率: 0.9998
  距离: 170.4136
  内存使用: 4.7571
  能量使用: 5.0776
  推理时间: 1.1232秒

批次 71:
  奖励值: 366.7260
  收益率: 0.9998
  距离: 167.0817
  内存使用: 4.6027
  能量使用: 5.0639
  推理时间: 1.1139秒

批次 72:
  奖励值: 359.5147
  收益率: 0.9998
  距离: 169.6388
  内存使用: 4.6306
  能量使用: 4.9012
  推理时间: 1.2426秒

批次 73:
  奖励值: 354.9720
  收益率: 0.9998
  距离: 161.5042
  内存使用: 4.5972
  能量使用: 4.9552
  推理时间: 1.1215秒

批次 74:
  奖励值: 375.7385
  收益率: 0.9998
  距离: 173.9110
  内存使用: 4.6043
  能量使用: 4.8078
  推理时间: 1.2903秒

批次 75:
  奖励值: 382.6601
  收益率: 0.9998
  距离: 169.7183
  内存使用: 4.6464
  能量使用: 4.9156
  推理时间: 1.1273秒

批次 76:
  奖励值: 360.0262
  收益率: 0.9996
  距离: 162.7346
  内存使用: 4.6709
  能量使用: 4.7422
  推理时间: 1.1215秒

批次 77:
  奖励值: 374.1898
  收益率: 0.9998
  距离: 170.3313
  内存使用: 4.7901
  能量使用: 4.8991
  推理时间: 1.1296秒

批次 78:
  奖励值: 355.7494
  收益率: 0.9998
  距离: 174.1297
  内存使用: 4.7299
  能量使用: 5.0422
  推理时间: 1.2666秒

批次 79:
  奖励值: 365.9523
  收益率: 0.9998
  距离: 164.3665
  内存使用: 4.7519
  能量使用: 4.9773
  推理时间: 1.1154秒

批次 80:
  奖励值: 367.5444
  收益率: 0.9998
  距离: 168.7661
  内存使用: 4.7340
  能量使用: 5.0117
  推理时间: 1.1245秒

批次 81:
  奖励值: 364.8394
  收益率: 0.9998
  距离: 172.8923
  内存使用: 4.6472
  能量使用: 4.9826
  推理时间: 1.1197秒

批次 82:
  奖励值: 355.8467
  收益率: 0.9998
  距离: 168.0867
  内存使用: 4.5988
  能量使用: 5.2219
  推理时间: 1.2523秒

批次 83:
  奖励值: 362.7244
  收益率: 0.9998
  距离: 166.2740
  内存使用: 4.5920
  能量使用: 4.8857
  推理时间: 1.1188秒

批次 84:
  奖励值: 357.2119
  收益率: 0.9998
  距离: 165.9474
  内存使用: 4.7018
  能量使用: 4.9278
  推理时间: 1.3157秒

批次 85:
  奖励值: 363.4477
  收益率: 0.9998
  距离: 167.5007
  内存使用: 4.6128
  能量使用: 4.9919
  推理时间: 1.1212秒

批次 86:
  奖励值: 347.4384
  收益率: 0.9998
  距离: 167.9940
  内存使用: 4.6841
  能量使用: 5.0453
  推理时间: 1.1241秒

批次 87:
  奖励值: 348.4599
  收益率: 0.9998
  距离: 166.5065
  内存使用: 4.7157
  能量使用: 5.0581
  推理时间: 1.1241秒

批次 88:
  奖励值: 361.7678
  收益率: 0.9998
  距离: 158.2164
  内存使用: 4.7240
  能量使用: 4.9249
  推理时间: 1.3067秒

批次 89:
  奖励值: 365.0336
  收益率: 0.9998
  距离: 169.3462
  内存使用: 4.6883
  能量使用: 4.8986
  推理时间: 1.1672秒

批次 90:
  奖励值: 365.2778
  收益率: 0.9998
  距离: 166.5875
  内存使用: 4.6045
  能量使用: 5.2528
  推理时间: 1.2991秒

批次 91:
  奖励值: 360.7681
  收益率: 0.9998
  距离: 163.4642
  内存使用: 4.6422
  能量使用: 4.9581
  推理时间: 1.1241秒

批次 92:
  奖励值: 358.3564
  收益率: 0.9998
  距离: 174.2019
  内存使用: 4.7082
  能量使用: 4.9779
  推理时间: 1.1239秒

批次 93:
  奖励值: 371.4079
  收益率: 0.9998
  距离: 166.2051
  内存使用: 4.7546
  能量使用: 5.0249
  推理时间: 1.1378秒

批次 94:
  奖励值: 368.4395
  收益率: 0.9998
  距离: 170.6043
  内存使用: 4.8005
  能量使用: 4.9166
  推理时间: 1.1158秒

批次 95:
  奖励值: 365.7786
  收益率: 0.9998
  距离: 170.3179
  内存使用: 4.6307
  能量使用: 4.8946
  推理时间: 1.2661秒

批次 96:
  奖励值: 363.5203
  收益率: 0.9998
  距离: 163.0454
  内存使用: 4.6189
  能量使用: 5.0956
  推理时间: 1.1280秒

批次 97:
  奖励值: 364.2809
  收益率: 0.9998
  距离: 170.6149
  内存使用: 4.5996
  能量使用: 5.0240
  推理时间: 1.1286秒

批次 98:
  奖励值: 355.5909
  收益率: 0.9998
  距离: 165.4522
  内存使用: 4.6182
  能量使用: 5.1481
  推理时间: 1.2455秒

批次 99:
  奖励值: 365.7515
  收益率: 0.9998
  距离: 165.9120
  内存使用: 4.6478
  能量使用: 5.1380
  推理时间: 1.1274秒

批次 100:
  奖励值: 358.4895
  收益率: 0.9998
  距离: 163.7604
  内存使用: 4.6297
  能量使用: 5.0316
  推理时间: 1.2937秒


==================== 总结 ====================
平均收益率: 0.9998
平均能量使用: 4.9833
平均推理时间: 1.1785秒
