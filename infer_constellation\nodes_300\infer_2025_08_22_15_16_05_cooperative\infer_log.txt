推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 116.6003
  收益率: 0.9993
  距离: 46.6173
  内存使用: 1.1483
  能量使用: 1.4339
  推理时间: 0.3993秒

批次 2:
  奖励值: 110.8334
  收益率: 0.9993
  距离: 47.4376
  内存使用: 1.2039
  能量使用: 1.4918
  推理时间: 0.3547秒

批次 3:
  奖励值: 109.9347
  收益率: 0.9993
  距离: 51.6601
  内存使用: 1.2157
  能量使用: 1.4547
  推理时间: 0.3515秒

批次 4:
  奖励值: 111.0347
  收益率: 0.9993
  距离: 48.9177
  内存使用: 1.2575
  能量使用: 1.5552
  推理时间: 0.3837秒

批次 5:
  奖励值: 120.4361
  收益率: 0.9993
  距离: 50.0024
  内存使用: 1.1788
  能量使用: 1.5466
  推理时间: 0.4576秒

批次 6:
  奖励值: 114.4962
  收益率: 0.9993
  距离: 49.3130
  内存使用: 1.1848
  能量使用: 1.5098
  推理时间: 0.4418秒

批次 7:
  奖励值: 112.8044
  收益率: 0.9993
  距离: 51.8490
  内存使用: 1.1237
  能量使用: 1.6187
  推理时间: 0.3914秒

批次 8:
  奖励值: 108.1202
  收益率: 0.9985
  距离: 51.5664
  内存使用: 1.1676
  能量使用: 1.4254
  推理时间: 0.3500秒

批次 9:
  奖励值: 112.8929
  收益率: 0.9993
  距离: 51.5554
  内存使用: 1.1214
  能量使用: 1.5373
  推理时间: 0.4737秒

批次 10:
  奖励值: 108.3977
  收益率: 0.9993
  距离: 48.7658
  内存使用: 1.1984
  能量使用: 1.4402
  推理时间: 0.3663秒

批次 11:
  奖励值: 114.4524
  收益率: 0.9993
  距离: 52.0166
  内存使用: 1.1534
  能量使用: 1.5253
  推理时间: 0.3471秒

批次 12:
  奖励值: 110.9110
  收益率: 0.9993
  距离: 49.3636
  内存使用: 1.2178
  能量使用: 1.5966
  推理时间: 0.3890秒

批次 13:
  奖励值: 106.2647
  收益率: 0.9992
  距离: 48.4031
  内存使用: 1.1756
  能量使用: 1.4918
  推理时间: 0.3642秒

批次 14:
  奖励值: 110.9496
  收益率: 0.9993
  距离: 49.8097
  内存使用: 1.2309
  能量使用: 1.4602
  推理时间: 0.4238秒

批次 15:
  奖励值: 114.4334
  收益率: 0.9993
  距离: 49.0746
  内存使用: 1.1956
  能量使用: 1.4631
  推理时间: 0.4069秒

批次 16:
  奖励值: 109.9233
  收益率: 0.9993
  距离: 49.7731
  内存使用: 1.1207
  能量使用: 1.4596
  推理时间: 0.4796秒

批次 17:
  奖励值: 111.9432
  收益率: 0.9993
  距离: 46.2214
  内存使用: 1.2069
  能量使用: 1.4852
  推理时间: 0.4048秒

批次 18:
  奖励值: 106.0760
  收益率: 0.9992
  距离: 49.4471
  内存使用: 1.1057
  能量使用: 1.4952
  推理时间: 0.3839秒

批次 19:
  奖励值: 113.0235
  收益率: 0.9993
  距离: 47.4802
  内存使用: 1.1991
  能量使用: 1.4737
  推理时间: 0.3934秒

批次 20:
  奖励值: 103.8722
  收益率: 0.9992
  距离: 50.3490
  内存使用: 1.1380
  能量使用: 1.5686
  推理时间: 0.4610秒

批次 21:
  奖励值: 111.4612
  收益率: 0.9993
  距离: 50.0141
  内存使用: 1.1780
  能量使用: 1.4855
  推理时间: 0.3679秒

批次 22:
  奖励值: 107.8771
  收益率: 0.9993
  距离: 50.3671
  内存使用: 1.1696
  能量使用: 1.5091
  推理时间: 0.3931秒

批次 23:
  奖励值: 109.0709
  收益率: 0.9992
  距离: 45.1304
  内存使用: 1.2079
  能量使用: 1.5200
  推理时间: 0.4746秒

批次 24:
  奖励值: 116.4137
  收益率: 0.9993
  距离: 51.1294
  内存使用: 1.1367
  能量使用: 1.5065
  推理时间: 0.3629秒

批次 25:
  奖励值: 105.0910
  收益率: 0.9992
  距离: 50.7944
  内存使用: 1.2496
  能量使用: 1.5741
  推理时间: 0.3986秒

批次 26:
  奖励值: 112.1881
  收益率: 0.9993
  距离: 46.5776
  内存使用: 1.2258
  能量使用: 1.4203
  推理时间: 0.3474秒

批次 27:
  奖励值: 116.9035
  收益率: 0.9993
  距离: 49.6465
  内存使用: 1.2043
  能量使用: 1.5421
  推理时间: 0.3544秒

批次 28:
  奖励值: 115.5724
  收益率: 0.9993
  距离: 47.2022
  内存使用: 1.1656
  能量使用: 1.4874
  推理时间: 0.4414秒

批次 29:
  奖励值: 126.6910
  收益率: 0.9993
  距离: 49.3666
  内存使用: 1.1899
  能量使用: 1.4615
  推理时间: 0.4550秒

批次 30:
  奖励值: 111.2194
  收益率: 0.9993
  距离: 51.3031
  内存使用: 1.2288
  能量使用: 1.4292
  推理时间: 0.3761秒

批次 31:
  奖励值: 112.2992
  收益率: 0.9993
  距离: 47.1476
  内存使用: 1.1774
  能量使用: 1.4767
  推理时间: 0.4597秒

批次 32:
  奖励值: 117.0446
  收益率: 0.9993
  距离: 48.3844
  内存使用: 1.2173
  能量使用: 1.5090
  推理时间: 0.3858秒

批次 33:
  奖励值: 104.9712
  收益率: 0.9992
  距离: 51.5841
  内存使用: 1.1873
  能量使用: 1.4862
  推理时间: 0.3760秒

批次 34:
  奖励值: 101.8956
  收益率: 0.9992
  距离: 51.4684
  内存使用: 1.2340
  能量使用: 1.5065
  推理时间: 0.3816秒

批次 35:
  奖励值: 112.4759
  收益率: 0.9993
  距离: 47.2385
  内存使用: 1.2085
  能量使用: 1.4012
  推理时间: 0.3623秒

批次 36:
  奖励值: 110.6876
  收益率: 0.9985
  距离: 49.9234
  内存使用: 1.2046
  能量使用: 1.4968
  推理时间: 0.3736秒

批次 37:
  奖励值: 112.3149
  收益率: 0.9993
  距离: 48.3445
  内存使用: 1.2176
  能量使用: 1.4080
  推理时间: 0.4381秒

批次 38:
  奖励值: 112.2793
  收益率: 0.9993
  距离: 49.9081
  内存使用: 1.1832
  能量使用: 1.5501
  推理时间: 0.4797秒

批次 39:
  奖励值: 104.9305
  收益率: 0.9992
  距离: 48.2135
  内存使用: 1.2282
  能量使用: 1.4973
  推理时间: 0.4689秒

批次 40:
  奖励值: 118.0053
  收益率: 0.9993
  距离: 44.4449
  内存使用: 1.1753
  能量使用: 1.3693
  推理时间: 0.4009秒

批次 41:
  奖励值: 105.9277
  收益率: 0.9992
  距离: 50.3525
  内存使用: 1.1185
  能量使用: 1.4736
  推理时间: 0.3548秒

批次 42:
  奖励值: 113.2806
  收益率: 0.9993
  距离: 48.9716
  内存使用: 1.2149
  能量使用: 1.4522
  推理时间: 0.3533秒

批次 43:
  奖励值: 112.7596
  收益率: 0.9993
  距离: 49.3817
  内存使用: 1.2324
  能量使用: 1.4666
  推理时间: 0.4156秒

批次 44:
  奖励值: 107.6753
  收益率: 0.9985
  距离: 50.4301
  内存使用: 1.1561
  能量使用: 1.4632
  推理时间: 0.3598秒

批次 45:
  奖励值: 107.7993
  收益率: 0.9993
  距离: 50.2633
  内存使用: 1.2675
  能量使用: 1.4705
  推理时间: 0.4613秒

批次 46:
  奖励值: 113.4489
  收益率: 0.9993
  距离: 48.7345
  内存使用: 1.2260
  能量使用: 1.5416
  推理时间: 0.3845秒

批次 47:
  奖励值: 114.0202
  收益率: 0.9993
  距离: 52.0513
  内存使用: 1.2154
  能量使用: 1.4929
  推理时间: 0.4349秒

批次 48:
  奖励值: 110.7001
  收益率: 0.9986
  距离: 53.6650
  内存使用: 1.2861
  能量使用: 1.4487
  推理时间: 0.3567秒

批次 49:
  奖励值: 109.9022
  收益率: 0.9993
  距离: 49.6757
  内存使用: 1.2158
  能量使用: 1.5040
  推理时间: 0.3581秒

批次 50:
  奖励值: 108.8823
  收益率: 0.9985
  距离: 50.9837
  内存使用: 1.2197
  能量使用: 1.4319
  推理时间: 0.4407秒

批次 51:
  奖励值: 112.3780
  收益率: 0.9993
  距离: 46.1205
  内存使用: 1.1919
  能量使用: 1.5316
  推理时间: 0.4682秒

批次 52:
  奖励值: 107.7985
  收益率: 0.9993
  距离: 52.6411
  内存使用: 1.2473
  能量使用: 1.5145
  推理时间: 0.3644秒

批次 53:
  奖励值: 111.3055
  收益率: 0.9993
  距离: 48.3419
  内存使用: 1.1640
  能量使用: 1.4830
  推理时间: 0.4557秒

批次 54:
  奖励值: 102.7874
  收益率: 0.9992
  距离: 54.4648
  内存使用: 1.2075
  能量使用: 1.5530
  推理时间: 0.4606秒

批次 55:
  奖励值: 107.6797
  收益率: 0.9993
  距离: 51.3297
  内存使用: 1.2063
  能量使用: 1.5045
  推理时间: 0.4185秒

批次 56:
  奖励值: 106.6189
  收益率: 0.9992
  距离: 49.2878
  内存使用: 1.2101
  能量使用: 1.4643
  推理时间: 0.4577秒

批次 57:
  奖励值: 118.6504
  收益率: 0.9993
  距离: 48.7115
  内存使用: 1.2872
  能量使用: 1.5004
  推理时间: 0.3508秒

批次 58:
  奖励值: 114.1326
  收益率: 0.9993
  距离: 50.6623
  内存使用: 1.2112
  能量使用: 1.4613
  推理时间: 0.3872秒

批次 59:
  奖励值: 109.7795
  收益率: 0.9993
  距离: 52.6729
  内存使用: 1.2340
  能量使用: 1.5340
  推理时间: 0.4743秒

批次 60:
  奖励值: 112.2501
  收益率: 0.9993
  距离: 46.4203
  内存使用: 1.2375
  能量使用: 1.4420
  推理时间: 0.3813秒

批次 61:
  奖励值: 105.3375
  收益率: 0.9992
  距离: 48.0654
  内存使用: 1.2129
  能量使用: 1.6465
  推理时间: 0.3813秒

批次 62:
  奖励值: 111.5263
  收益率: 0.9985
  距离: 44.9922
  内存使用: 1.2367
  能量使用: 1.5184
  推理时间: 0.4730秒

批次 63:
  奖励值: 106.8563
  收益率: 0.9993
  距离: 51.1050
  内存使用: 1.2397
  能量使用: 1.5427
  推理时间: 0.3821秒

批次 64:
  奖励值: 110.3232
  收益率: 0.9993
  距离: 49.4227
  内存使用: 1.2300
  能量使用: 1.5009
  推理时间: 0.3696秒

批次 65:
  奖励值: 113.2902
  收益率: 0.9993
  距离: 51.4838
  内存使用: 1.2469
  能量使用: 1.4888
  推理时间: 0.3742秒

批次 66:
  奖励值: 115.9504
  收益率: 0.9993
  距离: 49.9687
  内存使用: 1.2557
  能量使用: 1.4748
  推理时间: 0.4782秒

批次 67:
  奖励值: 105.1341
  收益率: 0.9992
  距离: 52.5067
  内存使用: 1.2607
  能量使用: 1.5644
  推理时间: 0.4662秒

批次 68:
  奖励值: 117.7029
  收益率: 0.9986
  距离: 46.3002
  内存使用: 1.2579
  能量使用: 1.4362
  推理时间: 0.3976秒

批次 69:
  奖励值: 113.1583
  收益率: 0.9993
  距离: 49.5490
  内存使用: 1.2510
  能量使用: 1.4834
  推理时间: 0.4638秒

批次 70:
  奖励值: 109.2376
  收益率: 0.9993
  距离: 48.4225
  内存使用: 1.1710
  能量使用: 1.5314
  推理时间: 0.4682秒

批次 71:
  奖励值: 108.9989
  收益率: 0.9985
  距离: 52.1714
  内存使用: 1.1792
  能量使用: 1.4517
  推理时间: 0.3999秒

批次 72:
  奖励值: 112.8654
  收益率: 0.9993
  距离: 47.3782
  内存使用: 1.2458
  能量使用: 1.4452
  推理时间: 0.3447秒

批次 73:
  奖励值: 110.9366
  收益率: 0.9985
  距离: 48.5449
  内存使用: 1.1280
  能量使用: 1.4539
  推理时间: 0.3525秒

批次 74:
  奖励值: 110.9974
  收益率: 0.9993
  距离: 49.4699
  内存使用: 1.2407
  能量使用: 1.4946
  推理时间: 0.4629秒

批次 75:
  奖励值: 104.7875
  收益率: 0.9992
  距离: 46.9401
  内存使用: 1.2571
  能量使用: 1.4279
  推理时间: 0.3584秒

批次 76:
  奖励值: 111.8511
  收益率: 0.9993
  距离: 51.3612
  内存使用: 1.1884
  能量使用: 1.5481
  推理时间: 0.3969秒

批次 77:
  奖励值: 110.0182
  收益率: 0.9993
  距离: 52.5932
  内存使用: 1.2481
  能量使用: 1.5224
  推理时间: 0.4654秒

批次 78:
  奖励值: 115.4127
  收益率: 0.9993
  距离: 46.5464
  内存使用: 1.1677
  能量使用: 1.4604
  推理时间: 0.4787秒

批次 79:
  奖励值: 108.5674
  收益率: 0.9993
  距离: 48.3107
  内存使用: 1.1302
  能量使用: 1.4242
  推理时间: 0.4674秒

批次 80:
  奖励值: 115.2342
  收益率: 0.9993
  距离: 49.9002
  内存使用: 1.1394
  能量使用: 1.4919
  推理时间: 0.4505秒

批次 81:
  奖励值: 107.1096
  收益率: 0.9985
  距离: 45.0418
  内存使用: 1.1882
  能量使用: 1.5508
  推理时间: 0.3823秒

批次 82:
  奖励值: 117.8399
  收益率: 0.9993
  距离: 47.1161
  内存使用: 1.1538
  能量使用: 1.4503
  推理时间: 0.3715秒

批次 83:
  奖励值: 117.5815
  收益率: 0.9986
  距离: 47.0285
  内存使用: 1.1943
  能量使用: 1.4143
  推理时间: 0.4021秒

批次 84:
  奖励值: 111.0143
  收益率: 0.9985
  距离: 47.3566
  内存使用: 1.1723
  能量使用: 1.4426
  推理时间: 0.4668秒

批次 85:
  奖励值: 109.3596
  收益率: 0.9993
  距离: 50.0089
  内存使用: 1.2162
  能量使用: 1.4556
  推理时间: 0.3663秒

批次 86:
  奖励值: 107.7187
  收益率: 0.9993
  距离: 49.0681
  内存使用: 1.2109
  能量使用: 1.4835
  推理时间: 0.4018秒

批次 87:
  奖励值: 112.9581
  收益率: 0.9986
  距离: 48.7104
  内存使用: 1.2497
  能量使用: 1.5237
  推理时间: 0.3731秒

批次 88:
  奖励值: 113.2561
  收益率: 0.9993
  距离: 49.2615
  内存使用: 1.1971
  能量使用: 1.4293
  推理时间: 0.4533秒

批次 89:
  奖励值: 112.8193
  收益率: 0.9993
  距离: 48.1291
  内存使用: 1.1827
  能量使用: 1.4496
  推理时间: 0.3494秒

批次 90:
  奖励值: 114.8295
  收益率: 0.9993
  距离: 51.0251
  内存使用: 1.2391
  能量使用: 1.4766
  推理时间: 0.4297秒

批次 91:
  奖励值: 111.5434
  收益率: 0.9993
  距离: 48.2284
  内存使用: 1.1443
  能量使用: 1.5405
  推理时间: 0.3733秒

批次 92:
  奖励值: 111.0823
  收益率: 0.9993
  距离: 45.7672
  内存使用: 1.1510
  能量使用: 1.5172
  推理时间: 0.3901秒

批次 93:
  奖励值: 111.8387
  收益率: 0.9993
  距离: 46.2742
  内存使用: 1.1694
  能量使用: 1.4788
  推理时间: 0.4950秒

批次 94:
  奖励值: 111.1987
  收益率: 0.9993
  距离: 50.5888
  内存使用: 1.2196
  能量使用: 1.3941
  推理时间: 0.4333秒

批次 95:
  奖励值: 103.0165
  收益率: 0.9992
  距离: 50.5494
  内存使用: 1.1515
  能量使用: 1.6660
  推理时间: 0.4102秒

批次 96:
  奖励值: 114.3621
  收益率: 0.9993
  距离: 47.7523
  内存使用: 1.2464
  能量使用: 1.4771
  推理时间: 0.3752秒

批次 97:
  奖励值: 110.3698
  收益率: 0.9993
  距离: 50.8081
  内存使用: 1.1212
  能量使用: 1.5309
  推理时间: 0.4720秒

批次 98:
  奖励值: 110.8379
  收益率: 0.9993
  距离: 50.1152
  内存使用: 1.1409
  能量使用: 1.4681
  推理时间: 0.3645秒

批次 99:
  奖励值: 107.0384
  收益率: 0.9993
  距离: 53.4930
  内存使用: 1.1813
  能量使用: 1.4489
  推理时间: 0.4701秒

批次 100:
  奖励值: 111.4982
  收益率: 0.9993
  距离: 46.5393
  内存使用: 1.1739
  能量使用: 1.4903
  推理时间: 0.3500秒


==================== 总结 ====================
平均收益率: 0.9992
平均能量使用: 1.4900
平均推理时间: 0.4082秒
