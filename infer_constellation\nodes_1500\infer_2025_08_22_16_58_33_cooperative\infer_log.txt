推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 537.2505
  收益率: 0.9999
  距离: 253.5832
  内存使用: 7.2441
  能量使用: 7.2722
  推理时间: 1.9537秒

批次 2:
  奖励值: 543.8955
  收益率: 0.9999
  距离: 246.8469
  内存使用: 7.3400
  能量使用: 7.4228
  推理时间: 1.8039秒

批次 3:
  奖励值: 532.6711
  收益率: 0.9998
  距离: 244.6482
  内存使用: 7.2344
  能量使用: 7.5761
  推理时间: 1.7755秒

批次 4:
  奖励值: 552.9791
  收益率: 0.9999
  距离: 253.1820
  内存使用: 7.2194
  能量使用: 7.4405
  推理时间: 1.7941秒

批次 5:
  奖励值: 541.4294
  收益率: 0.9999
  距离: 250.0164
  内存使用: 7.1549
  能量使用: 7.5711
  推理时间: 1.7605秒

批次 6:
  奖励值: 563.6758
  收益率: 0.9999
  距离: 243.5185
  内存使用: 7.1269
  能量使用: 7.4031
  推理时间: 1.7542秒

批次 7:
  奖励值: 551.7454
  收益率: 0.9999
  距离: 250.3673
  内存使用: 7.2385
  能量使用: 7.5052
  推理时间: 1.7744秒

批次 8:
  奖励值: 548.1915
  收益率: 0.9999
  距离: 244.3124
  内存使用: 7.0407
  能量使用: 7.6646
  推理时间: 1.7759秒

批次 9:
  奖励值: 547.2034
  收益率: 0.9999
  距离: 241.8698
  内存使用: 7.2376
  能量使用: 7.4872
  推理时间: 1.7505秒

批次 10:
  奖励值: 538.5786
  收益率: 0.9999
  距离: 245.8707
  内存使用: 7.2408
  能量使用: 7.5329
  推理时间: 1.7899秒

批次 11:
  奖励值: 537.5769
  收益率: 0.9999
  距离: 257.3632
  内存使用: 7.3771
  能量使用: 7.5064
  推理时间: 1.7476秒

批次 12:
  奖励值: 550.6493
  收益率: 0.9999
  距离: 249.5586
  内存使用: 7.2148
  能量使用: 7.3294
  推理时间: 1.7547秒

批次 13:
  奖励值: 540.5824
  收益率: 0.9999
  距离: 251.9662
  内存使用: 7.0993
  能量使用: 7.3705
  推理时间: 1.7488秒

批次 14:
  奖励值: 548.2241
  收益率: 0.9999
  距离: 247.9171
  内存使用: 7.1702
  能量使用: 7.4654
  推理时间: 1.9248秒

批次 15:
  奖励值: 545.1625
  收益率: 0.9999
  距离: 246.1530
  内存使用: 7.0404
  能量使用: 7.4828
  推理时间: 2.0269秒

批次 16:
  奖励值: 538.1475
  收益率: 0.9999
  距离: 248.5736
  内存使用: 7.2340
  能量使用: 7.4987
  推理时间: 1.7691秒

批次 17:
  奖励值: 551.6273
  收益率: 0.9999
  距离: 246.0639
  内存使用: 7.2264
  能量使用: 7.4556
  推理时间: 1.7720秒

批次 18:
  奖励值: 529.0331
  收益率: 0.9998
  距离: 246.1688
  内存使用: 7.0701
  能量使用: 7.2949
  推理时间: 1.7680秒

批次 19:
  奖励值: 536.9084
  收益率: 0.9999
  距离: 257.4081
  内存使用: 7.2319
  能量使用: 7.5441
  推理时间: 2.8998秒

批次 20:
  奖励值: 539.7090
  收益率: 0.9999
  距离: 252.7961
  内存使用: 7.0884
  能量使用: 7.4986
  推理时间: 1.7649秒

批次 21:
  奖励值: 541.7270
  收益率: 0.9999
  距离: 251.1699
  内存使用: 7.1437
  能量使用: 7.4336
  推理时间: 1.8161秒

批次 22:
  奖励值: 559.0049
  收益率: 0.9999
  距离: 249.1382
  内存使用: 7.1668
  能量使用: 7.4854
  推理时间: 1.7873秒

批次 23:
  奖励值: 549.4630
  收益率: 0.9999
  距离: 254.1134
  内存使用: 7.2295
  能量使用: 7.5322
  推理时间: 1.7792秒

批次 24:
  奖励值: 527.3770
  收益率: 0.9998
  距离: 252.1382
  内存使用: 7.1476
  能量使用: 7.5609
  推理时间: 1.7546秒

批次 25:
  奖励值: 560.4221
  收益率: 0.9999
  距离: 240.0289
  内存使用: 7.2131
  能量使用: 7.5144
  推理时间: 1.7859秒

批次 26:
  奖励值: 542.5252
  收益率: 0.9999
  距离: 255.2910
  内存使用: 7.1955
  能量使用: 7.6641
  推理时间: 1.7641秒

批次 27:
  奖励值: 541.3605
  收益率: 0.9999
  距离: 246.4872
  内存使用: 7.1808
  能量使用: 7.6128
  推理时间: 1.7446秒

批次 28:
  奖励值: 543.7126
  收益率: 0.9999
  距离: 251.0425
  内存使用: 7.3057
  能量使用: 7.4273
  推理时间: 1.7713秒

批次 29:
  奖励值: 524.4590
  收益率: 0.9998
  距离: 253.6794
  内存使用: 7.0390
  能量使用: 7.3647
  推理时间: 1.7886秒

批次 30:
  奖励值: 532.6197
  收益率: 0.9997
  距离: 247.3193
  内存使用: 7.2107
  能量使用: 7.6313
  推理时间: 1.7744秒

批次 31:
  奖励值: 533.4535
  收益率: 0.9999
  距离: 251.4048
  内存使用: 7.3404
  能量使用: 7.5494
  推理时间: 1.7501秒

批次 32:
  奖励值: 554.3888
  收益率: 0.9999
  距离: 251.4627
  内存使用: 7.0517
  能量使用: 7.7088
  推理时间: 1.7664秒

批次 33:
  奖励值: 539.2459
  收益率: 0.9999
  距离: 251.5340
  内存使用: 7.2095
  能量使用: 7.5656
  推理时间: 1.7972秒

批次 34:
  奖励值: 539.4229
  收益率: 0.9999
  距离: 254.6936
  内存使用: 7.1330
  能量使用: 7.3287
  推理时间: 1.8614秒

批次 35:
  奖励值: 547.1669
  收益率: 0.9999
  距离: 246.1158
  内存使用: 7.2060
  能量使用: 7.5451
  推理时间: 1.9534秒

批次 36:
  奖励值: 542.7097
  收益率: 0.9999
  距离: 242.5327
  内存使用: 7.2172
  能量使用: 7.4310
  推理时间: 1.9452秒

批次 37:
  奖励值: 557.2645
  收益率: 0.9999
  距离: 247.3073
  内存使用: 7.2603
  能量使用: 7.7041
  推理时间: 1.7690秒

批次 38:
  奖励值: 555.8292
  收益率: 0.9999
  距离: 243.3328
  内存使用: 7.2216
  能量使用: 7.3876
  推理时间: 1.7596秒

批次 39:
  奖励值: 540.9662
  收益率: 0.9999
  距离: 255.7929
  内存使用: 7.1206
  能量使用: 7.5546
  推理时间: 1.7741秒

批次 40:
  奖励值: 548.0132
  收益率: 0.9999
  距离: 247.4006
  内存使用: 7.2008
  能量使用: 7.3736
  推理时间: 1.7739秒

批次 41:
  奖励值: 552.3845
  收益率: 0.9999
  距离: 248.1996
  内存使用: 7.1202
  能量使用: 7.5118
  推理时间: 1.7914秒

批次 42:
  奖励值: 545.4570
  收益率: 0.9999
  距离: 251.4343
  内存使用: 7.2441
  能量使用: 7.8077
  推理时间: 1.7599秒

批次 43:
  奖励值: 533.5612
  收益率: 0.9999
  距离: 252.5815
  内存使用: 7.4076
  能量使用: 7.4891
  推理时间: 1.7611秒

批次 44:
  奖励值: 536.3658
  收益率: 0.9999
  距离: 248.1909
  内存使用: 7.2646
  能量使用: 7.6133
  推理时间: 1.7855秒

批次 45:
  奖励值: 532.2960
  收益率: 0.9998
  距离: 245.5467
  内存使用: 7.0561
  能量使用: 7.4062
  推理时间: 1.7729秒

批次 46:
  奖励值: 558.4768
  收益率: 0.9999
  距离: 243.8488
  内存使用: 7.2527
  能量使用: 7.7455
  推理时间: 1.7516秒

批次 47:
  奖励值: 533.5593
  收益率: 0.9999
  距离: 251.8951
  内存使用: 7.0742
  能量使用: 7.5134
  推理时间: 1.7533秒

批次 48:
  奖励值: 540.2040
  收益率: 0.9999
  距离: 257.7903
  内存使用: 7.2460
  能量使用: 7.5569
  推理时间: 1.7878秒

批次 49:
  奖励值: 550.7131
  收益率: 0.9999
  距离: 251.3333
  内存使用: 7.4121
  能量使用: 7.4290
  推理时间: 1.7980秒

批次 50:
  奖励值: 535.3927
  收益率: 0.9999
  距离: 250.6263
  内存使用: 7.1366
  能量使用: 7.6529
  推理时间: 1.7712秒

批次 51:
  奖励值: 541.7144
  收益率: 0.9999
  距离: 243.8674
  内存使用: 7.2358
  能量使用: 7.4693
  推理时间: 1.7604秒

批次 52:
  奖励值: 531.9950
  收益率: 0.9998
  距离: 244.1719
  内存使用: 7.1761
  能量使用: 7.4632
  推理时间: 1.7545秒

批次 53:
  奖励值: 563.7224
  收益率: 0.9999
  距离: 243.8180
  内存使用: 7.1833
  能量使用: 7.5549
  推理时间: 1.7814秒

批次 54:
  奖励值: 546.8162
  收益率: 0.9999
  距离: 254.7782
  内存使用: 6.8461
  能量使用: 7.5441
  推理时间: 1.9613秒

批次 55:
  奖励值: 548.9602
  收益率: 0.9999
  距离: 251.0374
  内存使用: 7.1250
  能量使用: 7.3175
  推理时间: 1.7627秒

批次 56:
  奖励值: 541.3616
  收益率: 0.9999
  距离: 241.5243
  内存使用: 7.3352
  能量使用: 7.4180
  推理时间: 1.7461秒

批次 57:
  奖励值: 542.7524
  收益率: 0.9999
  距离: 248.5087
  内存使用: 7.2590
  能量使用: 7.5285
  推理时间: 1.7756秒

批次 58:
  奖励值: 545.9054
  收益率: 0.9999
  距离: 255.8009
  内存使用: 7.3157
  能量使用: 7.4736
  推理时间: 1.9622秒

批次 59:
  奖励值: 552.6079
  收益率: 0.9999
  距离: 244.4916
  内存使用: 7.2290
  能量使用: 7.4644
  推理时间: 1.9266秒

批次 60:
  奖励值: 559.2783
  收益率: 0.9999
  距离: 237.2480
  内存使用: 7.3348
  能量使用: 7.4615
  推理时间: 1.7538秒

批次 61:
  奖励值: 539.7859
  收益率: 0.9999
  距离: 245.7800
  内存使用: 7.2388
  能量使用: 7.4104
  推理时间: 1.7658秒

批次 62:
  奖励值: 541.4474
  收益率: 0.9999
  距离: 242.8064
  内存使用: 7.0867
  能量使用: 7.4127
  推理时间: 1.7495秒

批次 63:
  奖励值: 542.6712
  收益率: 0.9999
  距离: 251.6611
  内存使用: 7.2208
  能量使用: 7.3767
  推理时间: 1.7704秒

批次 64:
  奖励值: 533.4501
  收益率: 0.9999
  距离: 255.9709
  内存使用: 7.0519
  能量使用: 7.6778
  推理时间: 1.7545秒

批次 65:
  奖励值: 551.8579
  收益率: 0.9999
  距离: 246.2942
  内存使用: 7.0199
  能量使用: 7.5706
  推理时间: 1.7751秒

批次 66:
  奖励值: 556.2217
  收益率: 0.9999
  距离: 258.8414
  内存使用: 7.0944
  能量使用: 7.4220
  推理时间: 1.7589秒

批次 67:
  奖励值: 544.1603
  收益率: 0.9999
  距离: 247.3748
  内存使用: 7.0917
  能量使用: 7.2135
  推理时间: 1.7532秒

批次 68:
  奖励值: 550.1736
  收益率: 0.9999
  距离: 251.9313
  内存使用: 7.2955
  能量使用: 7.4271
  推理时间: 1.7686秒

批次 69:
  奖励值: 538.5173
  收益率: 0.9999
  距离: 248.4391
  内存使用: 7.2144
  能量使用: 7.5124
  推理时间: 1.8037秒

批次 70:
  奖励值: 545.6696
  收益率: 0.9999
  距离: 237.1889
  内存使用: 7.1586
  能量使用: 7.5139
  推理时间: 1.7766秒

批次 71:
  奖励值: 537.3776
  收益率: 0.9999
  距离: 252.7703
  内存使用: 7.2409
  能量使用: 7.6347
  推理时间: 1.7373秒

批次 72:
  奖励值: 542.6005
  收益率: 0.9999
  距离: 262.4361
  内存使用: 7.2361
  能量使用: 7.3281
  推理时间: 1.7564秒

批次 73:
  奖励值: 535.4698
  收益率: 0.9999
  距离: 257.6671
  内存使用: 7.2267
  能量使用: 7.3683
  推理时间: 1.8077秒

批次 74:
  奖励值: 543.5261
  收益率: 0.9999
  距离: 251.8266
  内存使用: 7.1777
  能量使用: 7.5448
  推理时间: 1.8076秒

批次 75:
  奖励值: 541.6382
  收益率: 0.9999
  距离: 247.8779
  内存使用: 7.0903
  能量使用: 7.3560
  推理时间: 1.8094秒

批次 76:
  奖励值: 553.6580
  收益率: 0.9999
  距离: 244.0268
  内存使用: 7.3135
  能量使用: 7.3443
  推理时间: 1.8251秒

批次 77:
  奖励值: 541.8947
  收益率: 0.9999
  距离: 252.8989
  内存使用: 7.3512
  能量使用: 7.3616
  推理时间: 1.8405秒

批次 78:
  奖励值: 543.9390
  收益率: 0.9999
  距离: 250.0108
  内存使用: 7.1587
  能量使用: 7.5532
  推理时间: 1.7831秒

批次 79:
  奖励值: 535.3344
  收益率: 0.9999
  距离: 247.8187
  内存使用: 7.3865
  能量使用: 7.5269
  推理时间: 1.8011秒

批次 80:
  奖励值: 528.6694
  收益率: 0.9998
  距离: 252.4642
  内存使用: 7.1242
  能量使用: 7.4741
  推理时间: 1.7778秒

批次 81:
  奖励值: 535.3945
  收益率: 0.9999
  距离: 250.8534
  内存使用: 7.3928
  能量使用: 7.5653
  推理时间: 1.7720秒

批次 82:
  奖励值: 559.3030
  收益率: 0.9999
  距离: 249.2558
  内存使用: 7.2139
  能量使用: 7.5249
  推理时间: 1.8025秒

批次 83:
  奖励值: 560.5063
  收益率: 0.9999
  距离: 249.7606
  内存使用: 7.2715
  能量使用: 7.5563
  推理时间: 1.7932秒

批次 84:
  奖励值: 551.0762
  收益率: 0.9999
  距离: 247.8231
  内存使用: 7.2045
  能量使用: 7.4213
  推理时间: 1.7962秒

批次 85:
  奖励值: 557.9843
  收益率: 0.9999
  距离: 249.1897
  内存使用: 7.1347
  能量使用: 7.5071
  推理时间: 1.8049秒

批次 86:
  奖励值: 551.9510
  收益率: 0.9999
  距离: 250.2181
  内存使用: 7.2006
  能量使用: 7.6798
  推理时间: 1.7919秒

批次 87:
  奖励值: 557.8572
  收益率: 0.9999
  距离: 249.9994
  内存使用: 7.1917
  能量使用: 7.4950
  推理时间: 1.7606秒

批次 88:
  奖励值: 534.0115
  收益率: 0.9999
  距离: 242.9443
  内存使用: 7.3277
  能量使用: 7.5066
  推理时间: 1.7782秒

批次 89:
  奖励值: 544.6680
  收益率: 0.9999
  距离: 252.9560
  内存使用: 7.1864
  能量使用: 7.5224
  推理时间: 1.7987秒

批次 90:
  奖励值: 553.1528
  收益率: 0.9999
  距离: 253.8468
  内存使用: 7.1282
  能量使用: 7.5195
  推理时间: 1.8188秒

批次 91:
  奖励值: 531.5856
  收益率: 0.9999
  距离: 257.6932
  内存使用: 7.3187
  能量使用: 7.4175
  推理时间: 1.7973秒

批次 92:
  奖励值: 543.7034
  收益率: 0.9999
  距离: 255.8080
  内存使用: 7.1900
  能量使用: 7.5957
  推理时间: 1.8151秒

批次 93:
  奖励值: 541.0637
  收益率: 0.9999
  距离: 250.3544
  内存使用: 7.2445
  能量使用: 7.4746
  推理时间: 1.8851秒

批次 94:
  奖励值: 550.9453
  收益率: 0.9999
  距离: 244.2893
  内存使用: 7.2217
  能量使用: 7.4000
  推理时间: 1.9523秒

批次 95:
  奖励值: 542.0042
  收益率: 0.9999
  距离: 247.0461
  内存使用: 7.0495
  能量使用: 7.2974
  推理时间: 1.9269秒

批次 96:
  奖励值: 539.5851
  收益率: 0.9999
  距离: 254.9624
  内存使用: 7.0899
  能量使用: 7.3791
  推理时间: 1.7682秒

批次 97:
  奖励值: 544.9935
  收益率: 0.9999
  距离: 250.2388
  内存使用: 7.0674
  能量使用: 7.5071
  推理时间: 1.7758秒

批次 98:
  奖励值: 546.2652
  收益率: 0.9999
  距离: 246.8101
  内存使用: 7.2702
  能量使用: 7.5905
  推理时间: 1.7904秒

批次 99:
  奖励值: 531.9813
  收益率: 0.9999
  距离: 258.5582
  内存使用: 7.2542
  能量使用: 7.4259
  推理时间: 1.7727秒

批次 100:
  奖励值: 545.1530
  收益率: 0.9999
  距离: 245.2958
  内存使用: 7.0970
  能量使用: 7.5018
  推理时间: 1.7652秒


==================== 总结 ====================
平均收益率: 0.9999
平均能量使用: 7.4904
平均推理时间: 1.8073秒
