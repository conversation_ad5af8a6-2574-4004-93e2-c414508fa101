"""
消融实验模型测试脚本
测试GPN+LSTM和PN+IndRNN两个消融模型的基本功能
"""
import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset
from ablation_models import create_ablation_model
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_model_creation():
    """测试模型创建"""
    print("测试消融模型创建...")
    
    # 创建测试数据集
    test_data = ConstellationSMPDataset(
        size=20,  # 使用较小的规模进行测试
        num_samples=100,
        seed=12345,
        memory_total=0.3,
        power_total=5.0,
        num_satellites=3
    )
    
    models_to_test = ['gpn_indrnn', 'gpn_lstm', 'pn_indrnn']
    
    for model_name in models_to_test:
        print(f"\n测试模型: {model_name}")
        try:
            model = create_ablation_model(
                ablation_model=model_name,
                static_size=args.static_size,
                dynamic_size=args.dynamic_size,
                hidden_size=args.hidden_size,
                num_satellites=args.num_satellites,
                rnn=args.rnn,
                num_layers=args.num_layers,
                update_fn=test_data.update_dynamic,
                mask_fn=test_data.update_mask,
                num_nodes=20,
                dropout=args.dropout,
                constellation_mode='cooperative',
                use_transformer=False,  # 先不使用Transformer进行测试
                transformer_config=None
            ).to(device)
            
            # 计算参数数量
            total_params = sum(p.numel() for p in model.parameters())
            print(f"✓ {model_name} 模型创建成功")
            print(f"  参数数量: {total_params:,}")
            
        except Exception as e:
            print(f"✗ {model_name} 模型创建失败: {e}")
            import traceback
            traceback.print_exc()

def test_model_forward():
    """测试模型前向传播"""
    print("\n测试模型前向传播...")
    
    # 创建测试数据集
    test_data = ConstellationSMPDataset(
        size=20,
        num_samples=10,
        seed=12345,
        memory_total=0.3,
        power_total=5.0,
        num_satellites=3
    )
    
    # 获取一个批次的数据
    static, dynamic, x0 = test_data[0]
    static = static.unsqueeze(0).to(device)  # 添加批次维度
    dynamic = dynamic.unsqueeze(0).to(device)
    
    print(f"输入数据形状:")
    print(f"  static: {static.shape}")
    print(f"  dynamic: {dynamic.shape}")
    
    models_to_test = ['gpn_indrnn', 'gpn_lstm', 'pn_indrnn']
    
    for model_name in models_to_test:
        print(f"\n测试 {model_name} 前向传播:")
        try:
            model = create_ablation_model(
                ablation_model=model_name,
                static_size=args.static_size,
                dynamic_size=args.dynamic_size,
                hidden_size=args.hidden_size,
                num_satellites=args.num_satellites,
                rnn=args.rnn,
                num_layers=args.num_layers,
                update_fn=test_data.update_dynamic,
                mask_fn=test_data.update_mask,
                num_nodes=20,
                dropout=args.dropout,
                constellation_mode='cooperative',
                use_transformer=False,
                transformer_config=None
            ).to(device)
            
            model.eval()
            with torch.no_grad():
                tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = model(static, dynamic)
                
                print(f"✓ {model_name} 前向传播成功")
                print(f"  tour_indices shape: {tour_indices.shape}")
                print(f"  satellite_indices shape: {satellite_indices.shape}")
                print(f"  tour_log_prob shape: {tour_log_prob.shape}")
                print(f"  satellite_log_prob shape: {satellite_log_prob.shape}")
                
                # 检查输出的合理性
                assert tour_indices.shape[0] == 1, "批次大小应为1"
                assert satellite_indices.shape[0] == 1, "批次大小应为1"
                assert tour_log_prob.shape[0] == 1, "批次大小应为1"
                assert satellite_log_prob.shape[0] == 1, "批次大小应为1"
                
                print(f"  输出数值范围检查通过")
                
        except Exception as e:
            print(f"✗ {model_name} 前向传播失败: {e}")
            import traceback
            traceback.print_exc()

def test_training_compatibility():
    """测试与训练脚本的兼容性"""
    print("\n测试训练兼容性...")
    
    # 模拟训练脚本中的模型创建过程
    test_data = ConstellationSMPDataset(
        size=args.num_nodes,
        num_samples=100,  # 小数据集
        seed=args.seed,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=args.num_satellites
    )
    
    models_to_test = ['gpn_indrnn', 'gpn_lstm', 'pn_indrnn']
    
    for model_name in models_to_test:
        print(f"\n测试 {model_name} 训练兼容性:")
        try:
            # 模拟train_multi_constellation_modes.py中的create_model_for_mode函数
            actor = create_ablation_model(
                model_name,
                args.static_size,
                args.dynamic_size,
                args.hidden_size,
                args.num_satellites,
                args.rnn,
                args.num_layers,
                test_data.update_dynamic,
                test_data.update_mask,
                args.num_nodes,
                args.dropout,
                'cooperative',
                args.use_transformer,
                None
            ).to(device)
            
            # 测试优化器创建
            from torch import optim
            optimizer = optim.Adam(actor.parameters(), lr=args.actor_lr)
            
            print(f"✓ {model_name} 训练兼容性测试通过")
            print(f"  模型参数: {sum(p.numel() for p in actor.parameters()):,}")
            print(f"  优化器创建成功")
            
        except Exception as e:
            print(f"✗ {model_name} 训练兼容性测试失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主测试函数"""
    print("🧪 消融实验模型测试")
    print("=" * 50)
    print(f"设备: {device}")
    print(f"PyTorch版本: {torch.__version__}")
    
    # 运行测试
    test_model_creation()
    test_model_forward()
    test_training_compatibility()
    
    print("\n" + "=" * 50)
    print("✅ 消融实验模型测试完成")
    print("\n使用方法:")
    print("1. 原始模型 (GPN + IndRNN):")
    print("   python train_multi_constellation_modes.py --ablation_mode --ablation_model gpn_indrnn")
    print("\n2. 消融实验1 (GPN + LSTM):")
    print("   python train_multi_constellation_modes.py --ablation_mode --ablation_model gpn_lstm")
    print("\n3. 消融实验2 (PN + IndRNN):")
    print("   python train_multi_constellation_modes.py --ablation_mode --ablation_model pn_indrnn")

if __name__ == '__main__':
    main()
