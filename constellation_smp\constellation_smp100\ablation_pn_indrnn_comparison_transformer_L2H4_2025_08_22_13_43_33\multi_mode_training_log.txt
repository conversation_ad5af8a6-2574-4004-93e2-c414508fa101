消融实验: PN_INDRNN
================================================================================
实验时间: 2025_08_22_13_43_33
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
消融模型: pn_indrnn
模型描述: PN + IndRNN (消融实验2)
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,208,965
  Critic参数数量: 494,285
  总参数数量: 3,703,250
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-22 13:43:37
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/16, loss: 772.790, reward: 38.227, critic_reward: 51.494, revenue_rate: 0.9898, distance: 16.3603, memory: 0.1935, power: 0.4904, lr: 0.000100, took: 21.839s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 38.444, revenue_rate: 0.9968, distance: 16.3902, memory: 0.1968, power: 0.4911
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33 (验证集奖励: 38.4439)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/16, loss: 33.470, reward: 38.179, critic_reward: 40.851, revenue_rate: 0.9906, distance: 16.3816, memory: 0.1898, power: 0.4905, lr: 0.000100, took: 20.873s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 38.327, revenue_rate: 0.9959, distance: 16.5383, memory: 0.1965, power: 0.4909
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/16, loss: 9.705, reward: 38.189, critic_reward: 37.846, revenue_rate: 0.9909, distance: 16.4181, memory: 0.1906, power: 0.4907, lr: 0.000100, took: 20.957s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 38.429, revenue_rate: 0.9971, distance: 16.4517, memory: 0.1961, power: 0.4910
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-22 13:45:46
训练总耗时: 0:02:09.143309
训练过程统计:
  最终训练奖励: 38.0262
  最佳验证奖励: 38.4439
  训练轮数完成: 48
  奖励提升: -0.2855
  平均每轮提升: -0.0059
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-22 13:45:52
测试结束时间: 2025-08-22 13:45:58
测试耗时: 0:00:06.449160

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 38.4439
  模型保存路径: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33
测试结果:
  平均收益率: 0.9971
  平均距离: 16.1956
  平均内存使用: 0.1952
  平均功耗: 0.4942
模型信息:
  Actor参数: 3,208,965
  Critic参数: 494,285
  总参数: 3,703,250
综合性能评分: 1.5282
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,208,965
  Critic参数数量: 494,285
  总参数数量: 3,703,250
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-22 13:46:03
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/16, loss: 186.855, reward: 38.194, critic_reward: 44.049, revenue_rate: 0.9893, distance: 16.3761, memory: 0.1945, power: 0.4905, lr: 0.000100, took: 23.583s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 38.372, revenue_rate: 0.9941, distance: 16.2801, memory: 0.1966, power: 0.4908
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_competitive_transformer_L2H4_2025_08_22_13_45_58 (验证集奖励: 38.3722)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/16, loss: 9.501, reward: 38.124, critic_reward: 38.248, revenue_rate: 0.9899, distance: 16.4257, memory: 0.1929, power: 0.4905, lr: 0.000100, took: 21.253s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 38.316, revenue_rate: 0.9927, distance: 16.2568, memory: 0.1970, power: 0.4911
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/16, loss: 25.313, reward: 38.207, critic_reward: 38.695, revenue_rate: 0.9907, distance: 16.3656, memory: 0.1906, power: 0.4906, lr: 0.000100, took: 21.442s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 38.245, revenue_rate: 0.9918, distance: 16.3169, memory: 0.1968, power: 0.4911
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-22 13:48:13
训练总耗时: 0:02:10.031416
训练过程统计:
  最终训练奖励: 38.1042
  最佳验证奖励: 38.3722
  训练轮数完成: 48
  奖励提升: -0.1609
  平均每轮提升: -0.0034
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_competitive_transformer_L2H4_2025_08_22_13_45_58\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-22 13:48:18
测试结束时间: 2025-08-22 13:48:25
测试耗时: 0:00:06.330578

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 38.3722
  模型保存路径: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_competitive_transformer_L2H4_2025_08_22_13_45_58
测试结果:
  平均收益率: 0.9912
  平均距离: 16.2618
  平均内存使用: 0.1943
  平均功耗: 0.4945
模型信息:
  Actor参数: 3,208,965
  Critic参数: 494,285
  总参数: 3,703,250
综合性能评分: 1.4370
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 3,405,829
  Critic参数数量: 691,149
  总参数数量: 4,096,978
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-22 13:48:29
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/16, loss: 367.437, reward: 38.177, critic_reward: 33.416, revenue_rate: 0.9901, distance: 16.4543, memory: 0.1920, power: 0.4907, lr: 0.000100, took: 20.966s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 38.401, revenue_rate: 0.9958, distance: 16.3864, memory: 0.1974, power: 0.4914
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_hybrid_transformer_L2H4_2025_08_22_13_48_25 (验证集奖励: 38.4014)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/16, loss: 30.103, reward: 38.245, critic_reward: 39.889, revenue_rate: 0.9902, distance: 16.4476, memory: 0.1928, power: 0.4900, lr: 0.000100, took: 21.903s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 38.410, revenue_rate: 0.9967, distance: 16.4426, memory: 0.2005, power: 0.4947
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_hybrid_transformer_L2H4_2025_08_22_13_48_25 (验证集奖励: 38.4101)
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/16, loss: 14.319, reward: 38.177, critic_reward: 37.675, revenue_rate: 0.9906, distance: 16.3758, memory: 0.1924, power: 0.4909, lr: 0.000100, took: 21.239s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 38.378, revenue_rate: 0.9958, distance: 16.4257, memory: 0.1998, power: 0.4942
[HYBRID] 训练完成
训练结束时间: 2025-08-22 13:50:37
训练总耗时: 0:02:08.573136
训练过程统计:
  最终训练奖励: 38.7206
  最佳验证奖励: 38.4101
  训练轮数完成: 48
  奖励提升: 0.3910
  平均每轮提升: 0.0081
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_hybrid_transformer_L2H4_2025_08_22_13_48_25\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-22 13:50:43
测试结束时间: 2025-08-22 13:50:49
测试耗时: 0:00:06.496159

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 38.4101
  模型保存路径: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_hybrid_transformer_L2H4_2025_08_22_13_48_25
测试结果:
  平均收益率: 0.9966
  平均距离: 16.4843
  平均内存使用: 0.1983
  平均功耗: 0.4976
模型信息:
  Actor参数: 3,405,829
  Critic参数: 691,149
  总参数: 4,096,978
综合性能评分: 1.3760
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 0:07:21.783182
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  38.4439  0.9971   16.1956  0.1952   0.4942   3,703,250 
competitive  38.3722  0.9912   16.2618  0.1943   0.4945   3,703,250 
hybrid       38.4101  0.9966   16.4843  0.1983   0.4976   4,096,978 

性能排名:
🏆 最高奖励: COOPERATIVE (38.4439)
💰 最高收益率: COOPERATIVE (0.9971)
🚀 最短距离: COOPERATIVE (16.1956)
⚡ 最低功耗: COOPERATIVE (0.4942)

💡 推荐模式: COOPERATIVE
   理由: 在奖励和收益率两个关键指标上都表现最佳

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33
   对比分析: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\comparison_results
   全局日志: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33
   competitive 模式: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_competitive_transformer_L2H4_2025_08_22_13_45_58
   hybrid 模式: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_hybrid_transformer_L2H4_2025_08_22_13_48_25
