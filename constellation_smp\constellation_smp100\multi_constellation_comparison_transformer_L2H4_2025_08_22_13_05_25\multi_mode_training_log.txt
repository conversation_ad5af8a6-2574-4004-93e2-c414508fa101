多星座模式训练实验
================================================================================
实验时间: 2025_08_22_13_05_25
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-22 13:11:41
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 704.217, reward: 12.801, critic_reward: 16.630, revenue_rate: 0.3215, distance: 4.6473, memory: -0.0974, power: 0.1399, lr: 0.000100, took: 43.354s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 41.482, reward: 17.886, critic_reward: 17.342, revenue_rate: 0.4471, distance: 6.2890, memory: -0.1414, power: 0.1904, lr: 0.000100, took: 50.405s
