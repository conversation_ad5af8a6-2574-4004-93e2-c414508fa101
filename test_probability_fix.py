"""
测试概率分布修复
专门测试Categorical分布的Simplex约束问题
"""
import torch
import torch.nn.functional as F
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset
from ablation_models import create_ablation_model
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_probability_distribution():
    """测试概率分布修复"""
    print("🔍 测试概率分布修复")
    print("=" * 50)
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=10,
        num_samples=2,
        seed=12345,
        memory_total=0.3,
        power_total=5.0,
        num_satellites=3
    )
    
    # 获取数据
    static, dynamic, x0 = test_data[0]
    static = static.unsqueeze(0).to(device)
    dynamic = dynamic.unsqueeze(0).to(device)
    
    print(f"输入数据形状:")
    print(f"  static: {static.shape}")
    print(f"  dynamic: {dynamic.shape}")
    
    # 测试mask_fn调用
    print(f"\n测试mask_fn调用:")
    try:
        mask, satellite_masks = test_data.update_mask(dynamic, static=static)
        print(f"✅ mask_fn调用成功")
        print(f"  mask shape: {mask.shape}")
        print(f"  mask 值范围: [{mask.min().item()}, {mask.max().item()}]")
        print(f"  mask 和: {mask.sum(dim=1)}")
        
        # 检查是否有全零行
        zero_rows = (mask.sum(dim=1) == 0).sum().item()
        if zero_rows > 0:
            print(f"⚠️ 发现 {zero_rows} 个样本的所有任务都被掩码")
        
    except Exception as e:
        print(f"❌ mask_fn调用失败: {e}")
        return
    
    # 模拟概率计算过程
    print(f"\n测试概率计算:")
    try:
        # 创建模拟的task_logits
        seq_len = static.size(2)
        task_logits = torch.randn(1, seq_len, device=device)
        print(f"  原始logits: {task_logits}")
        
        # 应用掩码
        masked_logits = task_logits + mask.log()
        print(f"  掩码后logits: {masked_logits}")
        
        # 计算概率
        task_probs = F.softmax(masked_logits, dim=1)
        print(f"  初始概率: {task_probs}")
        print(f"  概率和: {task_probs.sum(dim=1)}")
        
        # 应用修复逻辑
        probs_sum = task_probs.sum(dim=1, keepdim=True)
        valid_mask = (probs_sum > 1e-10).float()
        
        print(f"  有效掩码: {valid_mask.squeeze()}")
        
        # 修复概率分布
        fixed_probs = torch.where(
            valid_mask.expand_as(task_probs) > 0,
            task_probs / torch.max(probs_sum, torch.ones_like(probs_sum) * 1e-10),
            torch.ones_like(task_probs) / task_probs.size(1)
        )
        
        print(f"  修复后概率: {fixed_probs}")
        print(f"  修复后概率和: {fixed_probs.sum(dim=1)}")
        
        # 测试Categorical分布
        m = torch.distributions.Categorical(fixed_probs)
        idx = m.sample()
        log_prob = m.log_prob(idx)
        
        print(f"✅ Categorical分布创建成功")
        print(f"  采样结果: {idx}")
        print(f"  log概率: {log_prob}")
        
    except Exception as e:
        print(f"❌ 概率计算失败: {e}")
        import traceback
        traceback.print_exc()

def test_simple_forward():
    """测试简化的前向传播"""
    print(f"\n🚀 测试简化前向传播")
    print("-" * 30)
    
    try:
        # 创建GPN+LSTM模型
        test_data = ConstellationSMPDataset(
            size=5,  # 更小的规模
            num_samples=1,
            seed=12345,
            memory_total=0.3,
            power_total=5.0,
            num_satellites=3
        )
        
        model = create_ablation_model(
            'gpn_lstm',
            args.static_size, args.dynamic_size, args.hidden_size,
            args.num_satellites, args.rnn, args.num_layers,
            test_data.update_dynamic, test_data.update_mask,
            5, args.dropout, 'cooperative', False, None
        ).to(device)
        
        static, dynamic, x0 = test_data[0]
        static = static.unsqueeze(0).to(device)
        dynamic = dynamic.unsqueeze(0).to(device)
        
        model.eval()
        with torch.no_grad():
            tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = model(static, dynamic)
            
            print(f"✅ GPN+LSTM 前向传播成功")
            print(f"  tour_indices: {tour_indices}")
            print(f"  satellite_indices: {satellite_indices}")
            
    except Exception as e:
        print(f"❌ 简化前向传播失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_probability_distribution()
    test_simple_forward()
    
    print("\n" + "=" * 50)
    print("✅ 概率分布修复测试完成！")
    print("=" * 50)
