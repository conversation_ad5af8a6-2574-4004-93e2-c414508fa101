多星座模式训练详细日志
================================================================================

实验时间: 2025-08-22 13:50:54
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练1000, 验证100
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,208,965
  Critic参数数量: 494,285
  总参数数量: 3,703,250
训练结果:
  最佳验证奖励: 38.443949
测试性能:
  平均收益率: 0.997078
  平均距离: 16.195588
  平均内存使用: 0.195246
  平均功耗: 0.494225
  综合性能评分: 1.528249
文件路径:
  模型保存: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,208,965
  Critic参数数量: 494,285
  总参数数量: 3,703,250
训练结果:
  最佳验证奖励: 38.372234
测试性能:
  平均收益率: 0.991230
  平均距离: 16.261757
  平均内存使用: 0.194343
  平均功耗: 0.494491
  综合性能评分: 1.437006
文件路径:
  模型保存: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_competitive_transformer_L2H4_2025_08_22_13_45_58
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,405,829
  Critic参数数量: 691,149
  总参数数量: 4,096,978
训练结果:
  最佳验证奖励: 38.410100
测试性能:
  平均收益率: 0.996612
  平均距离: 16.484335
  平均内存使用: 0.198327
  平均功耗: 0.497552
  综合性能评分: 1.376008
文件路径:
  模型保存: constellation_smp\constellation_smp100\ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33\constellation_pn_indrnn_hybrid_transformer_L2H4_2025_08_22_13_48_25
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: cooperative (38.4439)
最佳收益率模式: cooperative (0.9971)
最短距离模式: cooperative (16.1956)
最低功耗模式: cooperative (0.4942)

推荐使用: cooperative 模式
推荐理由: 在关键性能指标上表现最佳
