推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 274.0665
  收益率: 0.9997
  距离: 125.0503
  内存使用: 3.5280
  能量使用: 3.6886
  推理时间: 1.0059秒

批次 2:
  奖励值: 272.9471
  收益率: 0.9994
  距离: 126.5592
  内存使用: 3.4544
  能量使用: 3.6924
  推理时间: 0.8415秒

批次 3:
  奖励值: 274.3004
  收益率: 0.9997
  距离: 116.4726
  内存使用: 3.4759
  能量使用: 3.8507
  推理时间: 0.8462秒

批次 4:
  奖励值: 269.5177
  收益率: 0.9997
  距离: 120.3297
  内存使用: 3.3278
  能量使用: 3.7072
  推理时间: 0.8451秒

批次 5:
  奖励值: 266.9249
  收益率: 0.9997
  距离: 119.6342
  内存使用: 3.4206
  能量使用: 3.6957
  推理时间: 0.8408秒

批次 6:
  奖励值: 264.4793
  收益率: 0.9994
  距离: 125.9829
  内存使用: 3.5784
  能量使用: 3.6805
  推理时间: 0.8420秒

批次 7:
  奖励值: 271.1342
  收益率: 0.9997
  距离: 127.3843
  内存使用: 3.4708
  能量使用: 3.6767
  推理时间: 0.8434秒

批次 8:
  奖励值: 274.9513
  收益率: 0.9997
  距离: 123.1292
  内存使用: 3.4092
  能量使用: 3.7592
  推理时间: 0.8466秒

批次 9:
  奖励值: 253.5477
  收益率: 0.9997
  距离: 132.2716
  内存使用: 3.3994
  能量使用: 3.8338
  推理时间: 0.8585秒

批次 10:
  奖励值: 283.1321
  收益率: 0.9997
  距离: 121.7309
  内存使用: 3.5153
  能量使用: 3.8899
  推理时间: 0.8467秒

批次 11:
  奖励值: 260.2579
  收益率: 0.9997
  距离: 127.4231
  内存使用: 3.4468
  能量使用: 3.6141
  推理时间: 0.9082秒

批次 12:
  奖励值: 276.9503
  收益率: 0.9997
  距离: 123.2531
  内存使用: 3.6233
  能量使用: 3.8233
  推理时间: 0.8520秒

批次 13:
  奖励值: 284.0603
  收益率: 0.9997
  距离: 117.3165
  内存使用: 3.4788
  能量使用: 3.6845
  推理时间: 0.8459秒

批次 14:
  奖励值: 262.6667
  收益率: 0.9997
  距离: 129.5855
  内存使用: 3.5253
  能量使用: 3.7560
  推理时间: 0.8488秒

批次 15:
  奖励值: 274.2480
  收益率: 0.9997
  距离: 121.8384
  内存使用: 3.4745
  能量使用: 3.7915
  推理时间: 0.9029秒

批次 16:
  奖励值: 278.3044
  收益率: 0.9997
  距离: 125.0575
  内存使用: 3.4441
  能量使用: 3.6896
  推理时间: 0.8453秒

批次 17:
  奖励值: 286.5718
  收益率: 0.9997
  距离: 123.3007
  内存使用: 3.4775
  能量使用: 3.6783
  推理时间: 0.8474秒

批次 18:
  奖励值: 265.9142
  收益率: 0.9997
  距离: 133.4035
  内存使用: 3.4505
  能量使用: 3.7179
  推理时间: 0.8480秒

批次 19:
  奖励值: 283.0058
  收益率: 0.9997
  距离: 124.0455
  内存使用: 3.5014
  能量使用: 3.6421
  推理时间: 0.8442秒

批次 20:
  奖励值: 273.0210
  收益率: 0.9997
  距离: 127.0985
  内存使用: 3.4376
  能量使用: 3.8222
  推理时间: 0.8869秒

批次 21:
  奖励值: 280.6434
  收益率: 0.9997
  距离: 131.2869
  内存使用: 3.3302
  能量使用: 3.6964
  推理时间: 0.8452秒

批次 22:
  奖励值: 275.3440
  收益率: 0.9997
  距离: 120.6305
  内存使用: 3.2926
  能量使用: 3.7891
  推理时间: 0.8456秒

批次 23:
  奖励值: 274.6057
  收益率: 0.9994
  距离: 124.8233
  内存使用: 3.5017
  能量使用: 3.6637
  推理时间: 0.8461秒

批次 24:
  奖励值: 270.5474
  收益率: 0.9997
  距离: 126.0026
  内存使用: 3.5951
  能量使用: 3.7077
  推理时间: 0.8457秒

批次 25:
  奖励值: 283.4857
  收益率: 0.9997
  距离: 124.9007
  内存使用: 3.5487
  能量使用: 3.7795
  推理时间: 0.8461秒

批次 26:
  奖励值: 267.7130
  收益率: 0.9997
  距离: 128.3499
  内存使用: 3.3792
  能量使用: 3.8450
  推理时间: 0.8642秒

批次 27:
  奖励值: 284.3250
  收益率: 0.9997
  距离: 124.9900
  内存使用: 3.3351
  能量使用: 3.8252
  推理时间: 0.8427秒

批次 28:
  奖励值: 278.7136
  收益率: 0.9997
  距离: 124.3602
  内存使用: 3.4140
  能量使用: 3.7987
  推理时间: 0.8430秒

批次 29:
  奖励值: 272.4349
  收益率: 0.9997
  距离: 125.7957
  内存使用: 3.3790
  能量使用: 3.7557
  推理时间: 1.8463秒

批次 30:
  奖励值: 285.9309
  收益率: 0.9997
  距离: 125.2093
  内存使用: 3.4809
  能量使用: 3.8481
  推理时间: 0.8487秒

批次 31:
  奖励值: 280.2083
  收益率: 0.9994
  距离: 124.0643
  内存使用: 3.5171
  能量使用: 3.8020
  推理时间: 0.8490秒

批次 32:
  奖励值: 280.3430
  收益率: 0.9997
  距离: 130.2344
  内存使用: 3.3073
  能量使用: 3.7724
  推理时间: 0.8385秒

批次 33:
  奖励值: 273.0084
  收益率: 0.9997
  距离: 127.2661
  内存使用: 3.4104
  能量使用: 3.7069
  推理时间: 0.8442秒

批次 34:
  奖励值: 270.4295
  收益率: 0.9997
  距离: 114.4214
  内存使用: 3.3681
  能量使用: 3.7516
  推理时间: 0.8405秒

批次 35:
  奖励值: 273.0939
  收益率: 0.9997
  距离: 127.0316
  内存使用: 3.3768
  能量使用: 3.8043
  推理时间: 0.8412秒

批次 36:
  奖励值: 271.3470
  收益率: 0.9997
  距离: 124.2918
  内存使用: 3.4010
  能量使用: 3.8136
  推理时间: 0.8446秒

批次 37:
  奖励值: 271.3962
  收益率: 0.9997
  距离: 119.6237
  内存使用: 3.5430
  能量使用: 3.8411
  推理时间: 0.8461秒

批次 38:
  奖励值: 277.2487
  收益率: 0.9997
  距离: 124.0013
  内存使用: 3.3455
  能量使用: 3.7559
  推理时间: 0.8476秒

批次 39:
  奖励值: 273.9019
  收益率: 0.9997
  距离: 126.4800
  内存使用: 3.4807
  能量使用: 3.6359
  推理时间: 0.9045秒

批次 40:
  奖励值: 272.8013
  收益率: 0.9997
  距离: 123.3238
  内存使用: 3.3547
  能量使用: 3.7190
  推理时间: 0.8456秒

批次 41:
  奖励值: 270.2042
  收益率: 0.9997
  距离: 121.2345
  内存使用: 3.5026
  能量使用: 3.8545
  推理时间: 0.8377秒

批次 42:
  奖励值: 289.5456
  收益率: 0.9997
  距离: 126.3956
  内存使用: 3.5549
  能量使用: 3.7587
  推理时间: 0.8401秒

批次 43:
  奖励值: 277.3245
  收益率: 0.9997
  距离: 121.4205
  内存使用: 3.2734
  能量使用: 3.6574
  推理时间: 0.8474秒

批次 44:
  奖励值: 280.7915
  收益率: 0.9997
  距离: 121.0483
  内存使用: 3.5443
  能量使用: 3.8247
  推理时间: 0.8933秒

批次 45:
  奖励值: 273.8743
  收益率: 0.9997
  距离: 114.8484
  内存使用: 3.3915
  能量使用: 3.8115
  推理时间: 0.8571秒

批次 46:
  奖励值: 279.7228
  收益率: 0.9997
  距离: 124.0270
  内存使用: 3.5317
  能量使用: 3.5959
  推理时间: 0.8466秒

批次 47:
  奖励值: 275.7877
  收益率: 0.9997
  距离: 126.7813
  内存使用: 3.4278
  能量使用: 3.6158
  推理时间: 0.8428秒

批次 48:
  奖励值: 271.8916
  收益率: 0.9994
  距离: 122.1810
  内存使用: 3.4636
  能量使用: 3.7721
  推理时间: 0.8487秒

批次 49:
  奖励值: 277.4965
  收益率: 0.9997
  距离: 120.4303
  内存使用: 3.3898
  能量使用: 3.5870
  推理时间: 0.9037秒

批次 50:
  奖励值: 282.1452
  收益率: 0.9997
  距离: 128.8061
  内存使用: 3.3667
  能量使用: 3.7368
  推理时间: 0.8426秒

批次 51:
  奖励值: 259.0688
  收益率: 0.9997
  距离: 124.7122
  内存使用: 3.3457
  能量使用: 3.8043
  推理时间: 0.8441秒

批次 52:
  奖励值: 279.3260
  收益率: 0.9997
  距离: 121.7168
  内存使用: 3.5422
  能量使用: 3.8890
  推理时间: 0.8904秒

批次 53:
  奖励值: 271.8470
  收益率: 0.9997
  距离: 121.1566
  内存使用: 3.4021
  能量使用: 3.7476
  推理时间: 0.8433秒

批次 54:
  奖励值: 268.4213
  收益率: 0.9997
  距离: 127.4645
  内存使用: 3.3782
  能量使用: 3.7152
  推理时间: 0.8434秒

批次 55:
  奖励值: 264.2747
  收益率: 0.9997
  距离: 126.6769
  内存使用: 3.4109
  能量使用: 3.7633
  推理时间: 0.8431秒

批次 56:
  奖励值: 268.7115
  收益率: 0.9997
  距离: 125.8318
  内存使用: 3.4804
  能量使用: 3.6651
  推理时间: 0.8471秒

批次 57:
  奖励值: 278.5770
  收益率: 0.9997
  距离: 120.6300
  内存使用: 3.3253
  能量使用: 3.6909
  推理时间: 0.9121秒

批次 58:
  奖励值: 283.3145
  收益率: 0.9997
  距离: 120.8092
  内存使用: 3.5255
  能量使用: 3.8363
  推理时间: 0.8449秒

批次 59:
  奖励值: 279.8355
  收益率: 0.9997
  距离: 120.7962
  内存使用: 3.4437
  能量使用: 3.8893
  推理时间: 0.8449秒

批次 60:
  奖励值: 263.4862
  收益率: 0.9997
  距离: 118.7130
  内存使用: 3.3411
  能量使用: 3.7736
  推理时间: 0.8552秒

批次 61:
  奖励值: 276.0243
  收益率: 0.9994
  距离: 120.5770
  内存使用: 3.4042
  能量使用: 3.7706
  推理时间: 0.9100秒

批次 62:
  奖励值: 268.7560
  收益率: 0.9997
  距离: 128.9233
  内存使用: 3.5444
  能量使用: 3.8205
  推理时间: 0.8921秒

批次 63:
  奖励值: 267.4440
  收益率: 0.9997
  距离: 124.8054
  内存使用: 3.3961
  能量使用: 3.7106
  推理时间: 0.8457秒

批次 64:
  奖励值: 276.1338
  收益率: 0.9997
  距离: 122.8851
  内存使用: 3.3957
  能量使用: 3.6519
  推理时间: 0.9006秒

批次 65:
  奖励值: 259.4521
  收益率: 0.9997
  距离: 131.1726
  内存使用: 3.3592
  能量使用: 3.7641
  推理时间: 0.8653秒

批次 66:
  奖励值: 266.4329
  收益率: 0.9994
  距离: 129.4847
  内存使用: 3.3723
  能量使用: 3.6774
  推理时间: 0.9502秒

批次 67:
  奖励值: 269.1911
  收益率: 0.9997
  距离: 122.1857
  内存使用: 3.3353
  能量使用: 3.6972
  推理时间: 0.8473秒

批次 68:
  奖励值: 277.2750
  收益率: 0.9997
  距离: 124.6051
  内存使用: 3.4371
  能量使用: 3.8079
  推理时间: 0.8392秒

批次 69:
  奖励值: 280.6836
  收益率: 0.9997
  距离: 122.8541
  内存使用: 3.5610
  能量使用: 3.8177
  推理时间: 0.8362秒

批次 70:
  奖励值: 271.2249
  收益率: 0.9997
  距离: 125.0893
  内存使用: 3.4128
  能量使用: 3.8484
  推理时间: 0.9066秒

批次 71:
  奖励值: 268.8648
  收益率: 0.9997
  距离: 127.9981
  内存使用: 3.3631
  能量使用: 3.7093
  推理时间: 0.8470秒

批次 72:
  奖励值: 278.5279
  收益率: 0.9997
  距离: 126.1646
  内存使用: 3.5218
  能量使用: 3.6581
  推理时间: 0.8534秒

批次 73:
  奖励值: 275.6546
  收益率: 0.9997
  距离: 123.6085
  内存使用: 3.5551
  能量使用: 3.5274
  推理时间: 0.8510秒

批次 74:
  奖励值: 269.3152
  收益率: 0.9997
  距离: 124.6431
  内存使用: 3.5073
  能量使用: 3.6193
  推理时间: 0.8431秒

批次 75:
  奖励值: 267.7424
  收益率: 0.9997
  距离: 125.4304
  内存使用: 3.5861
  能量使用: 3.6991
  推理时间: 0.8421秒

批次 76:
  奖励值: 268.4912
  收益率: 0.9997
  距离: 131.6490
  内存使用: 3.5017
  能量使用: 3.6669
  推理时间: 0.8446秒

批次 77:
  奖励值: 282.0388
  收益率: 0.9997
  距离: 123.6745
  内存使用: 3.3444
  能量使用: 3.7037
  推理时间: 0.9193秒

批次 78:
  奖励值: 280.6461
  收益率: 0.9997
  距离: 126.8384
  内存使用: 3.5099
  能量使用: 3.7595
  推理时间: 0.8472秒

批次 79:
  奖励值: 281.2420
  收益率: 0.9997
  距离: 124.1544
  内存使用: 3.3917
  能量使用: 3.7699
  推理时间: 0.8396秒

批次 80:
  奖励值: 270.4301
  收益率: 0.9997
  距离: 124.5175
  内存使用: 3.3884
  能量使用: 3.8342
  推理时间: 0.8537秒

批次 81:
  奖励值: 263.4543
  收益率: 0.9997
  距离: 122.3069
  内存使用: 3.4727
  能量使用: 3.7122
  推理时间: 0.8615秒

批次 82:
  奖励值: 271.7259
  收益率: 0.9997
  距离: 120.6011
  内存使用: 3.4773
  能量使用: 3.6701
  推理时间: 0.8712秒

批次 83:
  奖励值: 266.1895
  收益率: 0.9997
  距离: 121.0005
  内存使用: 3.3697
  能量使用: 3.6508
  推理时间: 0.8450秒

批次 84:
  奖励值: 279.0162
  收益率: 0.9994
  距离: 124.9873
  内存使用: 3.5150
  能量使用: 3.6657
  推理时间: 0.8411秒

批次 85:
  奖励值: 267.8737
  收益率: 0.9994
  距离: 127.8971
  内存使用: 3.3277
  能量使用: 3.6279
  推理时间: 0.8442秒

批次 86:
  奖励值: 271.8504
  收益率: 0.9997
  距离: 128.7095
  内存使用: 3.3514
  能量使用: 3.8386
  推理时间: 0.8423秒

批次 87:
  奖励值: 276.9840
  收益率: 0.9994
  距离: 119.6543
  内存使用: 3.4315
  能量使用: 3.7464
  推理时间: 0.8416秒

批次 88:
  奖励值: 279.7399
  收益率: 0.9997
  距离: 116.1382
  内存使用: 3.2986
  能量使用: 3.6834
  推理时间: 0.8376秒

批次 89:
  奖励值: 269.8581
  收益率: 0.9997
  距离: 122.8521
  内存使用: 3.3768
  能量使用: 3.6551
  推理时间: 0.8472秒

批次 90:
  奖励值: 272.1467
  收益率: 0.9997
  距离: 124.5232
  内存使用: 3.4792
  能量使用: 3.7044
  推理时间: 0.8548秒

批次 91:
  奖励值: 279.4111
  收益率: 0.9997
  距离: 125.9929
  内存使用: 3.5581
  能量使用: 3.6274
  推理时间: 0.8461秒

批次 92:
  奖励值: 288.2747
  收益率: 0.9997
  距离: 122.5936
  内存使用: 3.3788
  能量使用: 3.6783
  推理时间: 0.8452秒

批次 93:
  奖励值: 283.8320
  收益率: 0.9997
  距离: 129.0880
  内存使用: 3.4192
  能量使用: 3.6289
  推理时间: 0.8427秒

批次 94:
  奖励值: 259.8076
  收益率: 0.9997
  距离: 121.9560
  内存使用: 3.4495
  能量使用: 3.7796
  推理时间: 0.8452秒

批次 95:
  奖励值: 267.4163
  收益率: 0.9997
  距离: 125.7325
  内存使用: 3.4594
  能量使用: 3.7758
  推理时间: 0.8611秒

批次 96:
  奖励值: 273.0716
  收益率: 0.9997
  距离: 117.6444
  内存使用: 3.4251
  能量使用: 3.7875
  推理时间: 0.9021秒

批次 97:
  奖励值: 267.3134
  收益率: 0.9997
  距离: 123.7831
  内存使用: 3.4645
  能量使用: 3.5256
  推理时间: 0.8965秒

批次 98:
  奖励值: 272.1949
  收益率: 0.9997
  距离: 126.9610
  内存使用: 3.3796
  能量使用: 3.6702
  推理时间: 0.8456秒

批次 99:
  奖励值: 281.8109
  收益率: 0.9994
  距离: 126.0214
  内存使用: 3.4916
  能量使用: 3.6655
  推理时间: 0.8473秒

批次 100:
  奖励值: 285.0177
  收益率: 0.9997
  距离: 120.5013
  内存使用: 3.4664
  能量使用: 3.7971
  推理时间: 0.8421秒


==================== 总结 ====================
平均收益率: 0.9997
平均能量使用: 3.7340
平均推理时间: 0.8674秒
