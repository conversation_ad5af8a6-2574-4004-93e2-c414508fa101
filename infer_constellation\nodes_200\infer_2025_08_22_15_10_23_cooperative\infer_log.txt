推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 71.6436
  收益率: 0.9989
  距离: 33.7526
  内存使用: 0.6537
  能量使用: 0.9066
  推理时间: 0.2827秒

批次 2:
  奖励值: 75.7830
  收益率: 0.9989
  距离: 31.9825
  内存使用: 0.7230
  能量使用: 0.9285
  推理时间: 0.2795秒

批次 3:
  奖励值: 70.0938
  收益率: 0.9989
  距离: 35.5468
  内存使用: 0.6763
  能量使用: 0.9892
  推理时间: 0.3003秒

批次 4:
  奖励值: 75.8091
  收益率: 0.9989
  距离: 35.6381
  内存使用: 0.7469
  能量使用: 0.9967
  推理时间: 0.2730秒

批次 5:
  奖励值: 71.5192
  收益率: 0.9989
  距离: 33.6338
  内存使用: 0.7335
  能量使用: 0.9943
  推理时间: 0.3044秒

批次 6:
  奖励值: 79.1159
  收益率: 0.9990
  距离: 32.9044
  内存使用: 0.6388
  能量使用: 1.0251
  推理时间: 0.3280秒

批次 7:
  奖励值: 83.9533
  收益率: 0.9980
  距离: 27.8340
  内存使用: 0.6382
  能量使用: 1.0211
  推理时间: 0.2571秒

批次 8:
  奖励值: 76.0676
  收益率: 0.9989
  距离: 33.4762
  内存使用: 0.6548
  能量使用: 0.9338
  推理时间: 0.3134秒

批次 9:
  奖励值: 74.9263
  收益率: 0.9989
  距离: 32.5442
  内存使用: 0.6623
  能量使用: 0.9409
  推理时间: 0.2440秒

批次 10:
  奖励值: 79.2653
  收益率: 0.9990
  距离: 33.6040
  内存使用: 0.7581
  能量使用: 0.9073
  推理时间: 0.2946秒

批次 11:
  奖励值: 74.3674
  收益率: 0.9989
  距离: 34.0493
  内存使用: 0.6745
  能量使用: 0.9414
  推理时间: 0.3218秒

批次 12:
  奖励值: 73.0313
  收益率: 0.9989
  距离: 34.8476
  内存使用: 0.7027
  能量使用: 0.9869
  推理时间: 0.3101秒

批次 13:
  奖励值: 75.0957
  收益率: 0.9989
  距离: 30.5179
  内存使用: 0.7339
  能量使用: 0.9568
  推理时间: 0.3123秒

批次 14:
  奖励值: 80.5529
  收益率: 0.9990
  距离: 32.1098
  内存使用: 0.7010
  能量使用: 0.8833
  推理时间: 0.2410秒

批次 15:
  奖励值: 75.4735
  收益率: 0.9978
  距离: 31.6123
  内存使用: 0.6714
  能量使用: 0.9693
  推理时间: 0.3162秒

批次 16:
  奖励值: 76.7516
  收益率: 0.9989
  距离: 32.2130
  内存使用: 0.7168
  能量使用: 0.9669
  推理时间: 0.3136秒

批次 17:
  奖励值: 78.4322
  收益率: 0.9990
  距离: 33.4609
  内存使用: 0.7247
  能量使用: 0.9501
  推理时间: 0.3446秒

批次 18:
  奖励值: 78.1399
  收益率: 0.9990
  距离: 33.9172
  内存使用: 0.7548
  能量使用: 1.0481
  推理时间: 0.3296秒

批次 19:
  奖励值: 72.3248
  收益率: 0.9989
  距离: 34.3043
  内存使用: 0.6497
  能量使用: 0.9964
  推理时间: 0.3210秒

批次 20:
  奖励值: 74.8958
  收益率: 0.9989
  距离: 34.3262
  内存使用: 0.6927
  能量使用: 0.9894
  推理时间: 0.3260秒

批次 21:
  奖励值: 79.2375
  收益率: 0.9990
  距离: 33.1718
  内存使用: 0.7604
  能量使用: 0.9929
  推理时间: 0.3313秒

批次 22:
  奖励值: 74.3789
  收益率: 0.9978
  距离: 34.4169
  内存使用: 0.7248
  能量使用: 0.9005
  推理时间: 0.3151秒

批次 23:
  奖励值: 74.6673
  收益率: 0.9989
  距离: 34.5949
  内存使用: 0.6801
  能量使用: 0.9904
  推理时间: 0.3226秒

批次 24:
  奖励值: 77.0853
  收益率: 0.9989
  距离: 31.9369
  内存使用: 0.7588
  能量使用: 0.9338
  推理时间: 0.2769秒

批次 25:
  奖励值: 72.0284
  收益率: 0.9989
  距离: 35.3671
  内存使用: 0.7469
  能量使用: 1.0292
  推理时间: 0.3090秒

批次 26:
  奖励值: 75.7456
  收益率: 0.9989
  距离: 32.9784
  内存使用: 0.7098
  能量使用: 1.0205
  推理时间: 0.3281秒

批次 27:
  奖励值: 74.2321
  收益率: 0.9989
  距离: 33.3991
  内存使用: 0.7287
  能量使用: 1.0080
  推理时间: 0.3259秒

批次 28:
  奖励值: 69.6308
  收益率: 0.9989
  距离: 36.9159
  内存使用: 0.7496
  能量使用: 1.0729
  推理时间: 0.3288秒

批次 29:
  奖励值: 81.4502
  收益率: 0.9990
  距离: 31.6366
  内存使用: 0.6838
  能量使用: 0.9791
  推理时间: 0.3298秒

批次 30:
  奖励值: 72.0143
  收益率: 0.9989
  距离: 34.1665
  内存使用: 0.6960
  能量使用: 0.9089
  推理时间: 0.3046秒

批次 31:
  奖励值: 74.4648
  收益率: 0.9989
  距离: 30.7622
  内存使用: 0.6826
  能量使用: 1.0255
  推理时间: 0.3223秒

批次 32:
  奖励值: 72.5722
  收益率: 0.9989
  距离: 33.5501
  内存使用: 0.6844
  能量使用: 1.0211
  推理时间: 0.2777秒

批次 33:
  奖励值: 74.2171
  收益率: 0.9989
  距离: 32.0912
  内存使用: 0.6884
  能量使用: 0.9862
  推理时间: 0.2983秒

批次 34:
  奖励值: 75.2614
  收益率: 0.9978
  距离: 30.2201
  内存使用: 0.7255
  能量使用: 0.9315
  推理时间: 0.3308秒

批次 35:
  奖励值: 73.9393
  收益率: 0.9989
  距离: 31.8355
  内存使用: 0.6822
  能量使用: 1.0036
  推理时间: 0.3133秒

批次 36:
  奖励值: 71.8188
  收益率: 0.9989
  距离: 31.2369
  内存使用: 0.7032
  能量使用: 1.0222
  推理时间: 0.3280秒

批次 37:
  奖励值: 71.7928
  收益率: 0.9989
  距离: 36.4571
  内存使用: 0.5716
  能量使用: 0.9856
  推理时间: 0.3457秒

批次 38:
  奖励值: 76.2169
  收益率: 0.9989
  距离: 28.6391
  内存使用: 0.7038
  能量使用: 1.0233
  推理时间: 0.2380秒

批次 39:
  奖励值: 72.2168
  收益率: 0.9989
  距离: 34.9079
  内存使用: 0.6978
  能量使用: 0.9608
  推理时间: 0.3312秒

批次 40:
  奖励值: 72.1625
  收益率: 0.9989
  距离: 32.7486
  内存使用: 0.7127
  能量使用: 1.0137
  推理时间: 0.2993秒

批次 41:
  奖励值: 70.1181
  收益率: 0.9989
  距离: 34.2145
  内存使用: 0.5625
  能量使用: 0.9867
  推理时间: 0.3293秒

批次 42:
  奖励值: 74.8914
  收益率: 0.9978
  距离: 33.1640
  内存使用: 0.6959
  能量使用: 0.9574
  推理时间: 0.2334秒

批次 43:
  奖励值: 73.0227
  收益率: 0.9989
  距离: 36.7564
  内存使用: 0.6469
  能量使用: 0.9512
  推理时间: 0.2676秒

批次 44:
  奖励值: 72.3825
  收益率: 0.9989
  距离: 35.6051
  内存使用: 0.6933
  能量使用: 0.9366
  推理时间: 0.3364秒

批次 45:
  奖励值: 75.0740
  收益率: 0.9978
  距离: 30.3396
  内存使用: 0.7365
  能量使用: 0.9759
  推理时间: 0.3153秒

批次 46:
  奖励值: 75.5954
  收益率: 0.9989
  距离: 34.0480
  内存使用: 0.6123
  能量使用: 0.9490
  推理时间: 0.2753秒

批次 47:
  奖励值: 74.0284
  收益率: 0.9989
  距离: 30.3659
  内存使用: 0.6079
  能量使用: 0.9694
  推理时间: 0.2940秒

批次 48:
  奖励值: 71.2502
  收益率: 0.9989
  距离: 33.0209
  内存使用: 0.6741
  能量使用: 1.0045
  推理时间: 0.3124秒

批次 49:
  奖励值: 73.0581
  收益率: 0.9989
  距离: 31.5473
  内存使用: 0.7016
  能量使用: 1.0349
  推理时间: 0.3273秒

批次 50:
  奖励值: 73.8200
  收益率: 0.9989
  距离: 33.4209
  内存使用: 0.7097
  能量使用: 1.0293
  推理时间: 0.2539秒

批次 51:
  奖励值: 76.2844
  收益率: 0.9978
  距离: 31.3591
  内存使用: 0.6764
  能量使用: 0.9959
  推理时间: 0.2476秒

批次 52:
  奖励值: 76.5278
  收益率: 0.9989
  距离: 33.0152
  内存使用: 0.7754
  能量使用: 0.9539
  推理时间: 0.3450秒

批次 53:
  奖励值: 83.9522
  收益率: 0.9980
  距离: 30.9397
  内存使用: 0.6896
  能量使用: 1.0662
  推理时间: 0.3088秒

批次 54:
  奖励值: 73.6060
  收益率: 0.9967
  距离: 32.6172
  内存使用: 0.7089
  能量使用: 1.0618
  推理时间: 0.3443秒

批次 55:
  奖励值: 72.5187
  收益率: 0.9989
  距离: 32.0548
  内存使用: 0.7289
  能量使用: 0.9789
  推理时间: 0.2814秒

批次 56:
  奖励值: 80.5675
  收益率: 0.9979
  距离: 30.7091
  内存使用: 0.7202
  能量使用: 1.0357
  推理时间: 0.2348秒

批次 57:
  奖励值: 73.7659
  收益率: 0.9989
  距离: 33.2189
  内存使用: 0.6669
  能量使用: 0.9824
  推理时间: 0.2684秒

批次 58:
  奖励值: 75.9564
  收益率: 0.9989
  距离: 30.9470
  内存使用: 0.7165
  能量使用: 1.0238
  推理时间: 0.3300秒

批次 59:
  奖励值: 72.3290
  收益率: 0.9989
  距离: 35.0543
  内存使用: 0.6747
  能量使用: 1.0131
  推理时间: 0.3224秒

批次 60:
  奖励值: 73.8534
  收益率: 0.9989
  距离: 34.4452
  内存使用: 0.6213
  能量使用: 1.0266
  推理时间: 0.3164秒

批次 61:
  奖励值: 74.3462
  收益率: 0.9978
  距离: 34.8637
  内存使用: 0.6845
  能量使用: 0.9593
  推理时间: 0.3307秒

批次 62:
  奖励值: 74.3763
  收益率: 0.9989
  距离: 34.3271
  内存使用: 0.7300
  能量使用: 0.9903
  推理时间: 0.3315秒

批次 63:
  奖励值: 68.8248
  收益率: 0.9988
  距离: 33.7938
  内存使用: 0.7047
  能量使用: 1.0520
  推理时间: 0.2381秒

批次 64:
  奖励值: 74.9461
  收益率: 0.9989
  距离: 32.1609
  内存使用: 0.7325
  能量使用: 1.0144
  推理时间: 0.2629秒

批次 65:
  奖励值: 77.2309
  收益率: 0.9990
  距离: 34.5311
  内存使用: 0.6492
  能量使用: 0.9579
  推理时间: 0.2343秒

批次 66:
  奖励值: 75.9574
  收益率: 0.9989
  距离: 31.6206
  内存使用: 0.7143
  能量使用: 0.9501
  推理时间: 0.2950秒

批次 67:
  奖励值: 74.2115
  收益率: 0.9989
  距离: 32.7365
  内存使用: 0.6736
  能量使用: 0.9668
  推理时间: 0.3014秒

批次 68:
  奖励值: 73.5195
  收益率: 0.9978
  距离: 33.0491
  内存使用: 0.7277
  能量使用: 0.9841
  推理时间: 0.3106秒

批次 69:
  奖励值: 74.7340
  收益率: 0.9989
  距离: 33.0348
  内存使用: 0.6523
  能量使用: 1.0448
  推理时间: 0.2371秒

批次 70:
  奖励值: 73.5260
  收益率: 0.9989
  距离: 35.5287
  内存使用: 0.6564
  能量使用: 0.9628
  推理时间: 0.2915秒

批次 71:
  奖励值: 73.5611
  收益率: 0.9978
  距离: 34.2312
  内存使用: 0.6120
  能量使用: 1.0345
  推理时间: 0.3062秒

批次 72:
  奖励值: 71.2174
  收益率: 0.9989
  距离: 31.3812
  内存使用: 0.6427
  能量使用: 0.9413
  推理时间: 0.3512秒

批次 73:
  奖励值: 75.1044
  收益率: 0.9989
  距离: 31.8853
  内存使用: 0.6713
  能量使用: 1.0346
  推理时间: 0.3366秒

批次 74:
  奖励值: 72.7871
  收益率: 0.9989
  距离: 36.1356
  内存使用: 0.6474
  能量使用: 1.0428
  推理时间: 0.3417秒

批次 75:
  奖励值: 75.8902
  收益率: 0.9989
  距离: 33.2935
  内存使用: 0.7385
  能量使用: 0.9876
  推理时间: 0.3325秒

批次 76:
  奖励值: 74.9999
  收益率: 0.9978
  距离: 31.1982
  内存使用: 0.6565
  能量使用: 0.9456
  推理时间: 0.2799秒

批次 77:
  奖励值: 73.6115
  收益率: 0.9989
  距离: 32.3199
  内存使用: 0.6446
  能量使用: 1.0124
  推理时间: 0.3028秒

批次 78:
  奖励值: 70.4023
  收益率: 0.9978
  距离: 35.5085
  内存使用: 0.7118
  能量使用: 0.9751
  推理时间: 0.3599秒

批次 79:
  奖励值: 72.2084
  收益率: 0.9989
  距离: 33.1326
  内存使用: 0.6922
  能量使用: 0.9584
  推理时间: 0.3233秒

批次 80:
  奖励值: 80.5257
  收益率: 0.9990
  距离: 29.8824
  内存使用: 0.6752
  能量使用: 0.9909
  推理时间: 0.3276秒

批次 81:
  奖励值: 70.4172
  收益率: 0.9989
  距离: 32.6794
  内存使用: 0.9585
  能量使用: 0.9278
  推理时间: 0.3468秒

批次 82:
  奖励值: 74.5122
  收益率: 0.9989
  距离: 35.7704
  内存使用: 0.7866
  能量使用: 1.0186
  推理时间: 0.2468秒

批次 83:
  奖励值: 78.0211
  收益率: 0.9989
  距离: 29.9955
  内存使用: 0.7096
  能量使用: 1.0527
  推理时间: 0.2444秒

批次 84:
  奖励值: 72.3221
  收益率: 0.9989
  距离: 33.4880
  内存使用: 0.6545
  能量使用: 1.0133
  推理时间: 0.3220秒

批次 85:
  奖励值: 71.8315
  收益率: 0.9989
  距离: 32.5206
  内存使用: 0.6229
  能量使用: 0.9935
  推理时间: 0.3150秒

批次 86:
  奖励值: 67.3124
  收益率: 0.9988
  距离: 31.3668
  内存使用: 0.8190
  能量使用: 0.9894
  推理时间: 0.2977秒

批次 87:
  奖励值: 75.3391
  收益率: 0.9979
  距离: 34.1580
  内存使用: 0.7398
  能量使用: 1.0240
  推理时间: 0.3403秒

批次 88:
  奖励值: 76.1228
  收益率: 0.9989
  距离: 32.6892
  内存使用: 0.6921
  能量使用: 0.9729
  推理时间: 0.3289秒

批次 89:
  奖励值: 76.4075
  收益率: 0.9989
  距离: 29.1856
  内存使用: 0.6914
  能量使用: 1.1080
  推理时间: 0.2446秒

批次 90:
  奖励值: 78.8282
  收益率: 0.9979
  距离: 30.8556
  内存使用: 0.6911
  能量使用: 0.9968
  推理时间: 0.2654秒

批次 91:
  奖励值: 71.7951
  收益率: 0.9989
  距离: 33.6359
  内存使用: 0.5926
  能量使用: 0.9812
  推理时间: 0.2385秒

批次 92:
  奖励值: 73.4679
  收益率: 0.9978
  距离: 33.2155
  内存使用: 0.6864
  能量使用: 0.9622
  推理时间: 0.3063秒

批次 93:
  奖励值: 71.8055
  收益率: 0.9989
  距离: 34.2673
  内存使用: 0.7233
  能量使用: 0.9984
  推理时间: 0.2401秒

批次 94:
  奖励值: 74.3689
  收益率: 0.9989
  距离: 33.5712
  内存使用: 0.7155
  能量使用: 0.9754
  推理时间: 0.2384秒

批次 95:
  奖励值: 76.9636
  收益率: 0.9989
  距离: 33.1142
  内存使用: 0.6060
  能量使用: 0.9527
  推理时间: 0.2816秒

批次 96:
  奖励值: 75.3597
  收益率: 0.9989
  距离: 31.5922
  内存使用: 0.6682
  能量使用: 1.0201
  推理时间: 0.3057秒

批次 97:
  奖励值: 77.9493
  收益率: 0.9979
  距离: 33.0291
  内存使用: 0.6877
  能量使用: 0.9846
  推理时间: 0.2394秒

批次 98:
  奖励值: 72.2591
  收益率: 0.9989
  距离: 32.3506
  内存使用: 0.5987
  能量使用: 0.9325
  推理时间: 0.2581秒

批次 99:
  奖励值: 78.5823
  收益率: 0.9990
  距离: 34.3312
  内存使用: 0.7361
  能量使用: 0.9679
  推理时间: 0.3211秒

批次 100:
  奖励值: 70.8924
  收益率: 0.9989
  距离: 33.1365
  内存使用: 0.7010
  能量使用: 0.9776
  推理时间: 0.3254秒


==================== 总结 ====================
平均收益率: 0.9987
平均能量使用: 0.9873
平均推理时间: 0.3001秒
