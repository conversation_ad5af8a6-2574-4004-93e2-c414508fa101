消融实验: GPN_LSTM
================================================================================
实验时间: 2025_08_23_14_43_59
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
消融模型: gpn_lstm
模型描述: GPN + LSTM (消融实验1)
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-23 14:44:03
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
❌ cooperative 模式训练失败: Dimension out of range (expected to be in range of [-3, 2], but got 3)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 876, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 374, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 133, in train_constellation_with_detailed_logging
    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\ablation_models.py", line 83, in forward
    tour_indices, tour_log_prob = self._resource_aware_task_selection(constellation_features, static, dynamic)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\ablation_models.py", line 154, in _resource_aware_task_selection
    mask = self.mask_fn(static, current_dynamic)  # (batch_size, seq_len)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\constellation_smp\constellation_smp.py", line 352, in update_mask
    num_satellites = dynamic.size(3)
IndexError: Dimension out of range (expected to be in range of [-3, 2], but got 3)


================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-23 14:44:15
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
❌ competitive 模式训练失败: Dimension out of range (expected to be in range of [-3, 2], but got 3)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 876, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 374, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 133, in train_constellation_with_detailed_logging
    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\ablation_models.py", line 83, in forward
    tour_indices, tour_log_prob = self._resource_aware_task_selection(constellation_features, static, dynamic)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\ablation_models.py", line 154, in _resource_aware_task_selection
    mask = self.mask_fn(static, current_dynamic)  # (batch_size, seq_len)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\constellation_smp\constellation_smp.py", line 352, in update_mask
    num_satellites = dynamic.size(3)
IndexError: Dimension out of range (expected to be in range of [-3, 2], but got 3)


================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 4,059,400
  Critic参数数量: 691,149
  总参数数量: 4,750,549
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-23 14:44:24
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
❌ hybrid 模式训练失败: Dimension out of range (expected to be in range of [-3, 2], but got 3)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 876, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 374, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\train_multi_constellation_modes.py", line 133, in train_constellation_with_detailed_logging
    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\ablation_models.py", line 83, in forward
    tour_indices, tour_log_prob = self._resource_aware_task_selection(constellation_features, static, dynamic)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\ablation_models.py", line 154, in _resource_aware_task_selection
    mask = self.mask_fn(static, current_dynamic)  # (batch_size, seq_len)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0821single_smp\constellation_smp\constellation_smp.py", line 352, in update_mask
    num_satellites = dynamic.size(3)
IndexError: Dimension out of range (expected to be in range of [-3, 2], but got 3)

❌ 所有模式训练都失败了
