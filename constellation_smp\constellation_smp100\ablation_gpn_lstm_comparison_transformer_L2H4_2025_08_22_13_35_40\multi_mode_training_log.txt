消融实验: GPN_LSTM
================================================================================
实验时间: 2025_08_22_13_35_40
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
消融模型: gpn_lstm
模型描述: GPN + LSTM (消融实验1)
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-22 13:35:44
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/16, loss: 572.986, reward: 38.069, critic_reward: 33.823, revenue_rate: 0.9893, distance: 16.2936, memory: 0.2072, power: 0.4913, lr: 0.000100, took: 21.258s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 25.875, revenue_rate: 0.7040, distance: 13.9729, memory: 0.3687, power: 0.3761
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40 (验证集奖励: 25.8752)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/16, loss: 67.845, reward: 37.898, critic_reward: 35.937, revenue_rate: 0.9854, distance: 16.3607, memory: 0.2191, power: 0.4928, lr: 0.000100, took: 20.403s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 25.880, revenue_rate: 0.7045, distance: 14.0123, memory: 0.3630, power: 0.3756
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40 (验证集奖励: 25.8795)
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/16, loss: 27.633, reward: 38.671, critic_reward: 37.636, revenue_rate: 0.9993, distance: 16.2347, memory: 0.1632, power: 0.4900, lr: 0.000100, took: 20.321s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 25.958, revenue_rate: 0.7070, distance: 14.0817, memory: 0.3665, power: 0.3760
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40 (验证集奖励: 25.9579)
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-22 13:37:53
训练总耗时: 0:02:08.618495
训练过程统计:
  最终训练奖励: 38.8559
  最佳验证奖励: 25.9579
  训练轮数完成: 48
  奖励提升: 1.2344
  平均每轮提升: 0.0257
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-22 13:37:58
测试结束时间: 2025-08-22 13:38:06
测试耗时: 0:00:07.453650

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 25.9579
  模型保存路径: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40
测试结果:
  平均收益率: 0.7387
  平均距离: 14.5154
  平均内存使用: 0.1318
  平均功耗: 0.3588
模型信息:
  Actor参数: 3,862,536
  Critic参数: 494,285
  总参数: 4,356,821
综合性能评分: -0.1165
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-22 13:38:10
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/16, loss: 413.325, reward: 38.263, critic_reward: 40.809, revenue_rate: 0.9923, distance: 16.2037, memory: 0.1914, power: 0.4912, lr: 0.000100, took: 20.212s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 26.073, revenue_rate: 0.7055, distance: 13.7217, memory: 0.3576, power: 0.3754
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_competitive_transformer_L2H4_2025_08_22_13_38_06 (验证集奖励: 26.0727)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/16, loss: 23.879, reward: 38.102, critic_reward: 38.013, revenue_rate: 0.9887, distance: 16.2637, memory: 0.1986, power: 0.4896, lr: 0.000100, took: 20.243s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 25.837, revenue_rate: 0.7040, distance: 14.0461, memory: 0.3698, power: 0.3755
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/16, loss: 22.164, reward: 38.082, critic_reward: 38.962, revenue_rate: 0.9871, distance: 16.2150, memory: 0.1944, power: 0.4875, lr: 0.000100, took: 20.172s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 25.880, revenue_rate: 0.7055, distance: 14.1037, memory: 0.3599, power: 0.3748
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-22 13:40:16
训练总耗时: 0:02:06.208183
训练过程统计:
  最终训练奖励: 37.8655
  最佳验证奖励: 26.0727
  训练轮数完成: 48
  奖励提升: -0.2873
  平均每轮提升: -0.0060
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_competitive_transformer_L2H4_2025_08_22_13_38_06\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-22 13:40:21
测试结束时间: 2025-08-22 13:40:28
测试耗时: 0:00:07.191225

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 26.0727
  模型保存路径: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_competitive_transformer_L2H4_2025_08_22_13_38_06
测试结果:
  平均收益率: 0.7368
  平均距离: 14.3229
  平均内存使用: 0.1413
  平均功耗: 0.3598
模型信息:
  Actor参数: 3,862,536
  Critic参数: 494,285
  总参数: 4,356,821
综合性能评分: -0.0443
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 4,059,400
  Critic参数数量: 691,149
  总参数数量: 4,750,549
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-22 13:40:33
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/16, loss: 165.481, reward: 38.226, critic_reward: 37.577, revenue_rate: 0.9913, distance: 16.2230, memory: 0.2025, power: 0.4901, lr: 0.000100, took: 20.164s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 26.225, revenue_rate: 0.7111, distance: 13.9562, memory: 0.3576, power: 0.3756
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_hybrid_transformer_L2H4_2025_08_22_13_40_28 (验证集奖励: 26.2253)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/16, loss: 20.366, reward: 38.353, critic_reward: 38.123, revenue_rate: 0.9920, distance: 16.1793, memory: 0.1998, power: 0.4925, lr: 0.000100, took: 20.553s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 25.733, revenue_rate: 0.7020, distance: 14.0675, memory: 0.3696, power: 0.3767
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/16, loss: 19.928, reward: 38.294, critic_reward: 38.541, revenue_rate: 0.9905, distance: 16.3043, memory: 0.1887, power: 0.4892, lr: 0.000100, took: 19.968s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 26.682, revenue_rate: 0.7230, distance: 14.2454, memory: 0.2737, power: 0.3765
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_hybrid_transformer_L2H4_2025_08_22_13_40_28 (验证集奖励: 26.6822)
[HYBRID] 训练完成
训练结束时间: 2025-08-22 13:42:40
训练总耗时: 0:02:07.924373
训练过程统计:
  最终训练奖励: 38.0324
  最佳验证奖励: 26.6822
  训练轮数完成: 48
  奖励提升: -0.3742
  平均每轮提升: -0.0078
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_hybrid_transformer_L2H4_2025_08_22_13_40_28\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-22 13:42:46
测试结束时间: 2025-08-22 13:42:54
测试耗时: 0:00:08.398248

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 26.6822
  模型保存路径: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_hybrid_transformer_L2H4_2025_08_22_13_40_28
测试结果:
  平均收益率: 0.7362
  平均距离: 14.7640
  平均内存使用: 0.1289
  平均功耗: 0.3579
模型信息:
  Actor参数: 4,059,400
  Critic参数: 691,149
  总参数: 4,750,549
综合性能评分: -0.2634
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 0:07:19.654156
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  25.9579  0.7387   14.5154  0.1318   0.3588   4,356,821 
competitive  26.0727  0.7368   14.3229  0.1413   0.3598   4,356,821 
hybrid       26.6822  0.7362   14.7640  0.1289   0.3579   4,750,549 

性能排名:
🏆 最高奖励: HYBRID (26.6822)
💰 最高收益率: COOPERATIVE (0.7387)
🚀 最短距离: COMPETITIVE (14.3229)
⚡ 最低功耗: HYBRID (0.3579)

💡 推荐模式分析:
   如果追求最高奖励: HYBRID
   如果追求最高收益率: COOPERATIVE

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40
   对比分析: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\comparison_results
   全局日志: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_cooperative_transformer_L2H4_2025_08_22_13_35_40
   competitive 模式: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_competitive_transformer_L2H4_2025_08_22_13_38_06
   hybrid 模式: constellation_smp\constellation_smp100\ablation_gpn_lstm_comparison_transformer_L2H4_2025_08_22_13_35_40\constellation_gpn_lstm_hybrid_transformer_L2H4_2025_08_22_13_40_28
