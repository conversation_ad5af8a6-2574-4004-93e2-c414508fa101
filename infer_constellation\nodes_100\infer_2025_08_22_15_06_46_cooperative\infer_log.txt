推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 36.1526
  收益率: 0.9978
  距离: 17.0118
  内存使用: 0.2108
  能量使用: 0.4721
  推理时间: 0.4575秒

批次 2:
  奖励值: 36.8439
  收益率: 0.9978
  距离: 18.2314
  内存使用: 0.2425
  能量使用: 0.4382
  推理时间: 0.1299秒

批次 3:
  奖励值: 36.9890
  收益率: 0.9978
  距离: 16.8752
  内存使用: 0.2127
  能量使用: 0.5342
  推理时间: 0.1427秒

批次 4:
  奖励值: 37.6416
  收益率: 0.9957
  距离: 16.0336
  内存使用: 0.1652
  能量使用: 0.5179
  推理时间: 0.1357秒

批次 5:
  奖励值: 36.0223
  收益率: 0.9977
  距离: 15.0712
  内存使用: 0.1829
  能量使用: 0.5014
  推理时间: 0.1542秒

批次 6:
  奖励值: 39.8985
  收益率: 0.9919
  距离: 16.9491
  内存使用: 0.1726
  能量使用: 0.4813
  推理时间: 0.1874秒

批次 7:
  奖励值: 36.6788
  收益率: 0.9978
  距离: 15.5201
  内存使用: 0.1888
  能量使用: 0.5334
  推理时间: 0.1824秒

批次 8:
  奖励值: 37.4637
  收益率: 0.9978
  距离: 17.2110
  内存使用: 0.1588
  能量使用: 0.5027
  推理时间: 0.1396秒

批次 9:
  奖励值: 38.9527
  收益率: 0.9978
  距离: 14.1805
  内存使用: 0.1966
  能量使用: 0.5175
  推理时间: 0.2095秒

批次 10:
  奖励值: 38.7854
  收益率: 0.9958
  距离: 16.2064
  内存使用: 0.4978
  能量使用: 0.5251
  推理时间: 0.1777秒

批次 11:
  奖励值: 37.4149
  收益率: 0.9978
  距离: 14.8820
  内存使用: 0.1732
  能量使用: 0.5150
  推理时间: 0.1255秒

批次 12:
  奖励值: 40.2323
  收益率: 0.9979
  距离: 14.2929
  内存使用: 0.1739
  能量使用: 0.4685
  推理时间: 0.1313秒

批次 13:
  奖励值: 38.5529
  收益率: 0.9979
  距离: 18.8098
  内存使用: 0.1903
  能量使用: 0.4940
  推理时间: 0.1692秒

批次 14:
  奖励值: 36.7471
  收益率: 0.9956
  距离: 15.5894
  内存使用: 0.1913
  能量使用: 0.5252
  推理时间: 0.1703秒

批次 15:
  奖励值: 39.1836
  收益率: 0.9958
  距离: 15.8939
  内存使用: 0.2098
  能量使用: 0.5292
  推理时间: 0.1781秒

批次 16:
  奖励值: 39.2917
  收益率: 0.9959
  距离: 16.8027
  内存使用: 0.1675
  能量使用: 0.4464
  推理时间: 0.1966秒

批次 17:
  奖励值: 37.7971
  收益率: 0.9957
  距离: 16.4324
  内存使用: 0.2175
  能量使用: 0.5559
  推理时间: 0.1296秒

批次 18:
  奖励值: 35.4357
  收益率: 0.9978
  距离: 17.7724
  内存使用: 0.2096
  能量使用: 0.5467
  推理时间: 0.1238秒

批次 19:
  奖励值: 39.4039
  收益率: 0.9979
  距离: 16.9080
  内存使用: 0.1698
  能量使用: 0.5144
  推理时间: 0.1274秒

批次 20:
  奖励值: 36.3565
  收益率: 0.9955
  距离: 15.8522
  内存使用: 0.1759
  能量使用: 0.4590
  推理时间: 0.1686秒

批次 21:
  奖励值: 38.1402
  收益率: 0.9979
  距离: 16.8604
  内存使用: 0.1549
  能量使用: 0.5043
  推理时间: 0.2352秒

批次 22:
  奖励值: 38.5793
  收益率: 0.9979
  距离: 16.0830
  内存使用: 0.2700
  能量使用: 0.4884
  推理时间: 0.1722秒

批次 23:
  奖励值: 38.6219
  收益率: 0.9957
  距离: 15.2330
  内存使用: 0.2380
  能量使用: 0.4853
  推理时间: 0.1717秒

批次 24:
  奖励值: 35.7237
  收益率: 0.9977
  距离: 14.9465
  内存使用: 0.2329
  能量使用: 0.5731
  推理时间: 0.1280秒

批次 25:
  奖励值: 40.2108
  收益率: 0.9979
  距离: 15.6988
  内存使用: 0.1614
  能量使用: 0.5182
  推理时间: 0.1561秒

批次 26:
  奖励值: 36.6626
  收益率: 0.9957
  距离: 17.9865
  内存使用: 0.1793
  能量使用: 0.5089
  推理时间: 0.1845秒

批次 27:
  奖励值: 40.0794
  收益率: 0.9980
  距离: 16.5561
  内存使用: 0.2074
  能量使用: 0.4776
  推理时间: 0.1739秒

批次 28:
  奖励值: 37.6727
  收益率: 0.9957
  距离: 16.1858
  内存使用: 0.2145
  能量使用: 0.4543
  推理时间: 0.2218秒

批次 29:
  奖励值: 36.8424
  收益率: 0.9978
  距离: 15.2689
  内存使用: 0.1665
  能量使用: 0.4797
  推理时间: 0.1261秒

批次 30:
  奖励值: 37.8170
  收益率: 0.9978
  距离: 15.8499
  内存使用: 0.1891
  能量使用: 0.5270
  推理时间: 0.1581秒

批次 31:
  奖励值: 41.0602
  收益率: 0.9980
  距离: 17.4385
  内存使用: 0.1709
  能量使用: 0.4701
  推理时间: 0.1723秒

批次 32:
  奖励值: 36.7280
  收益率: 0.9979
  距离: 19.4099
  内存使用: 0.2246
  能量使用: 0.5095
  推理时间: 0.1499秒

批次 33:
  奖励值: 37.4008
  收益率: 0.9979
  距离: 17.9356
  内存使用: 0.1610
  能量使用: 0.5019
  推理时间: 0.1794秒

批次 34:
  奖励值: 38.0605
  收益率: 0.9978
  距离: 16.0424
  内存使用: 0.1625
  能量使用: 0.4741
  推理时间: 0.1722秒

批次 35:
  奖励值: 38.3625
  收益率: 0.9958
  距离: 16.5902
  内存使用: 0.1818
  能量使用: 0.5030
  推理时间: 0.1777秒

批次 36:
  奖励值: 41.3023
  收益率: 0.9920
  距离: 16.1152
  内存使用: 0.1562
  能量使用: 0.5239
  推理时间: 0.1759秒

批次 37:
  奖励值: 33.5539
  收益率: 0.9976
  距离: 14.8194
  内存使用: 0.1811
  能量使用: 0.4918
  推理时间: 0.2082秒

批次 38:
  奖励值: 34.5213
  收益率: 0.9977
  距离: 16.9124
  内存使用: 0.1385
  能量使用: 0.5064
  推理时间: 0.1290秒

批次 39:
  奖励值: 39.6907
  收益率: 0.9979
  距离: 13.3756
  内存使用: 0.1352
  能量使用: 0.5078
  推理时间: 0.1707秒

批次 40:
  奖励值: 36.5058
  收益率: 0.9978
  距离: 16.1016
  内存使用: 0.1765
  能量使用: 0.5102
  推理时间: 0.1741秒

批次 41:
  奖励值: 35.3224
  收益率: 0.9977
  距离: 16.8990
  内存使用: 0.1861
  能量使用: 0.4701
  推理时间: 0.1951秒

批次 42:
  奖励值: 39.8267
  收益率: 0.9979
  距离: 16.1071
  内存使用: 0.1625
  能量使用: 0.4771
  推理时间: 0.1605秒

批次 43:
  奖励值: 37.5698
  收益率: 0.9978
  距离: 16.3821
  内存使用: 0.2010
  能量使用: 0.4774
  推理时间: 0.2381秒

批次 44:
  奖励值: 39.4272
  收益率: 0.9937
  距离: 15.5131
  内存使用: 0.1480
  能量使用: 0.4845
  推理时间: 0.1253秒

批次 45:
  奖励值: 37.8774
  收益率: 0.9978
  距离: 15.7924
  内存使用: 0.2119
  能量使用: 0.4408
  推理时间: 0.1289秒

批次 46:
  奖励值: 37.4866
  收益率: 0.9978
  距离: 16.5952
  内存使用: 0.1556
  能量使用: 0.4759
  推理时间: 0.1426秒

批次 47:
  奖励值: 37.1857
  收益率: 0.9978
  距离: 14.9394
  内存使用: 0.1825
  能量使用: 0.5068
  推理时间: 0.1818秒

批次 48:
  奖励值: 38.9648
  收益率: 0.9979
  距离: 14.9129
  内存使用: 0.4729
  能量使用: 0.4845
  推理时间: 0.1840秒

批次 49:
  奖励值: 41.0957
  收益率: 0.9980
  距离: 15.4160
  内存使用: 0.1491
  能量使用: 0.4435
  推理时间: 0.1734秒

批次 50:
  奖励值: 38.5658
  收益率: 0.9979
  距离: 17.7817
  内存使用: 0.1964
  能量使用: 0.4902
  推理时间: 0.1780秒

批次 51:
  奖励值: 38.1320
  收益率: 0.9978
  距离: 14.8490
  内存使用: 0.1868
  能量使用: 0.5002
  推理时间: 0.1620秒

批次 52:
  奖励值: 37.9693
  收益率: 0.9978
  距离: 15.5695
  内存使用: 0.2228
  能量使用: 0.4692
  推理时间: 0.1927秒

批次 53:
  奖励值: 38.5143
  收益率: 0.9979
  距离: 17.8657
  内存使用: 0.2342
  能量使用: 0.4715
  推理时间: 0.1695秒

批次 54:
  奖励值: 38.4840
  收益率: 0.9978
  距离: 13.7776
  内存使用: 0.1805
  能量使用: 0.4738
  推理时间: 0.1827秒

批次 55:
  奖励值: 39.5949
  收益率: 0.9958
  距离: 14.5381
  内存使用: 0.2016
  能量使用: 0.4706
  推理时间: 0.1455秒

批次 56:
  奖励值: 37.3088
  收益率: 0.9956
  距离: 14.5309
  内存使用: 0.1891
  能量使用: 0.4624
  推理时间: 0.1750秒

批次 57:
  奖励值: 36.1315
  收益率: 0.9978
  距离: 16.9890
  内存使用: 0.2081
  能量使用: 0.5399
  推理时间: 0.1793秒

批次 58:
  奖励值: 35.3156
  收益率: 0.9978
  距离: 18.2560
  内存使用: 0.1807
  能量使用: 0.5320
  推理时间: 0.1303秒

批次 59:
  奖励值: 41.1052
  收益率: 0.9941
  距离: 17.7297
  内存使用: 0.1607
  能量使用: 0.4991
  推理时间: 0.1320秒

批次 60:
  奖励值: 37.6635
  收益率: 0.9978
  距离: 16.3576
  内存使用: 0.2260
  能量使用: 0.4895
  推理时间: 0.1501秒

批次 61:
  奖励值: 37.3926
  收益率: 0.9978
  距离: 15.8776
  内存使用: 0.2095
  能量使用: 0.5277
  推理时间: 0.1743秒

批次 62:
  奖励值: 35.5916
  收益率: 0.9977
  距离: 13.7365
  内存使用: 0.1647
  能量使用: 0.5157
  推理时间: 0.1867秒

批次 63:
  奖励值: 34.9029
  收益率: 0.9955
  距离: 17.4611
  内存使用: 0.2370
  能量使用: 0.4961
  推理时间: 0.1837秒

批次 64:
  奖励值: 34.0535
  收益率: 0.9953
  距离: 16.2000
  内存使用: 0.1966
  能量使用: 0.4964
  推理时间: 0.1823秒

批次 65:
  奖励值: 40.5870
  收益率: 0.9959
  距离: 15.6513
  内存使用: 0.1637
  能量使用: 0.4111
  推理时间: 0.1778秒

批次 66:
  奖励值: 37.4788
  收益率: 0.9958
  距离: 18.7566
  内存使用: 0.1940
  能量使用: 0.4917
  推理时间: 0.1720秒

批次 67:
  奖励值: 38.8336
  收益率: 0.9978
  距离: 14.4278
  内存使用: 0.2013
  能量使用: 0.5038
  推理时间: 0.1647秒

批次 68:
  奖励值: 36.8165
  收益率: 0.9978
  距离: 15.0797
  内存使用: 0.2044
  能量使用: 0.4828
  推理时间: 0.1974秒

批次 69:
  奖励值: 37.4197
  收益率: 0.9978
  距离: 15.4475
  内存使用: 0.2023
  能量使用: 0.5108
  推理时间: 0.1705秒

批次 70:
  奖励值: 39.5167
  收益率: 0.9979
  距离: 17.2852
  内存使用: 0.1942
  能量使用: 0.4871
  推理时间: 0.1687秒

批次 71:
  奖励值: 37.3674
  收益率: 0.9979
  距离: 17.5737
  内存使用: 0.1720
  能量使用: 0.5196
  推理时间: 0.1650秒

批次 72:
  奖励值: 37.1506
  收益率: 0.9978
  距离: 15.6460
  内存使用: 0.1530
  能量使用: 0.4999
  推理时间: 0.1741秒

批次 73:
  奖励值: 42.3568
  收益率: 0.9980
  距离: 14.9454
  内存使用: 0.2096
  能量使用: 0.5314
  推理时间: 0.1286秒

批次 74:
  奖励值: 39.1627
  收益率: 0.9959
  距离: 18.1975
  内存使用: 0.1984
  能量使用: 0.4788
  推理时间: 0.1232秒

批次 75:
  奖励值: 36.7318
  收益率: 0.9956
  距离: 16.9014
  内存使用: 0.1881
  能量使用: 0.4468
  推理时间: 0.1293秒

批次 76:
  奖励值: 34.5355
  收益率: 0.9908
  距离: 16.0428
  内存使用: 0.1912
  能量使用: 0.4950
  推理时间: 0.1495秒

批次 77:
  奖励值: 39.8693
  收益率: 0.9980
  距离: 17.1935
  内存使用: 0.1874
  能量使用: 0.4805
  推理时间: 0.1809秒

批次 78:
  奖励值: 37.2327
  收益率: 0.9978
  距离: 15.4824
  内存使用: 0.1692
  能量使用: 0.4831
  推理时间: 0.1797秒

批次 79:
  奖励值: 33.8130
  收益率: 0.9977
  距离: 16.9353
  内存使用: 0.1662
  能量使用: 0.4725
  推理时间: 0.1782秒

批次 80:
  奖励值: 36.0156
  收益率: 0.9956
  距离: 16.8856
  内存使用: 0.1815
  能量使用: 0.5017
  推理时间: 0.1562秒

批次 81:
  奖励值: 30.6898
  收益率: 0.9975
  距离: 17.9493
  内存使用: 0.1970
  能量使用: 0.4741
  推理时间: 0.1856秒

批次 82:
  奖励值: 41.6480
  收益率: 0.9960
  距离: 14.3392
  内存使用: 0.2181
  能量使用: 0.5467
  推理时间: 0.1691秒

批次 83:
  奖励值: 42.2445
  收益率: 0.9980
  距离: 13.6070
  内存使用: 0.1835
  能量使用: 0.5205
  推理时间: 0.1675秒

批次 84:
  奖励值: 39.1680
  收益率: 0.9979
  距离: 16.4064
  内存使用: 0.2056
  能量使用: 0.4519
  推理时间: 0.1725秒

批次 85:
  奖励值: 39.2478
  收益率: 0.9937
  距离: 15.8144
  内存使用: 0.1809
  能量使用: 0.5091
  推理时间: 0.1665秒

批次 86:
  奖励值: 37.2681
  收益率: 0.9934
  距离: 14.5818
  内存使用: 0.1737
  能量使用: 0.5083
  推理时间: 0.1845秒

批次 87:
  奖励值: 36.2499
  收益率: 0.9978
  距离: 16.7890
  内存使用: 0.1737
  能量使用: 0.5374
  推理时间: 0.1909秒

批次 88:
  奖励值: 39.2875
  收益率: 0.9979
  距离: 16.2265
  内存使用: 0.1694
  能量使用: 0.4291
  推理时间: 0.1778秒

批次 89:
  奖励值: 35.9745
  收益率: 0.9978
  距离: 16.7831
  内存使用: 0.2185
  能量使用: 0.4494
  推理时间: 0.1758秒

批次 90:
  奖励值: 34.0150
  收益率: 0.9977
  距离: 18.2643
  内存使用: 0.1786
  能量使用: 0.5271
  推理时间: 0.1721秒

批次 91:
  奖励值: 37.5754
  收益率: 0.9957
  距离: 16.1735
  内存使用: 0.1733
  能量使用: 0.5025
  推理时间: 0.1713秒

批次 92:
  奖励值: 40.1306
  收益率: 0.9938
  距离: 15.8566
  内存使用: 0.1993
  能量使用: 0.4829
  推理时间: 0.1768秒

批次 93:
  奖励值: 42.0011
  收益率: 0.9980
  距离: 15.2631
  内存使用: 0.2307
  能量使用: 0.5039
  推理时间: 0.1886秒

批次 94:
  奖励值: 36.2410
  收益率: 0.9978
  距离: 15.8122
  内存使用: 0.1994
  能量使用: 0.5063
  推理时间: 0.1300秒

批次 95:
  奖励值: 39.1485
  收益率: 0.9959
  距离: 18.4803
  内存使用: 0.2002
  能量使用: 0.4225
  推理时间: 0.1213秒

批次 96:
  奖励值: 37.5616
  收益率: 0.9978
  距离: 16.8110
  内存使用: 0.2058
  能量使用: 0.4600
  推理时间: 0.1457秒

批次 97:
  奖励值: 36.9525
  收益率: 0.9978
  距离: 16.5719
  内存使用: 0.2275
  能量使用: 0.4956
  推理时间: 0.1661秒

批次 98:
  奖励值: 40.9805
  收益率: 0.9960
  距离: 16.8952
  内存使用: 0.2221
  能量使用: 0.5217
  推理时间: 0.1830秒

批次 99:
  奖励值: 33.2359
  收益率: 0.9976
  距离: 17.6275
  内存使用: 0.1899
  能量使用: 0.5108
  推理时间: 0.1884秒

批次 100:
  奖励值: 40.8636
  收益率: 0.9959
  距离: 15.6662
  内存使用: 0.1087
  能量使用: 0.4980
  推理时间: 0.1825秒


==================== 总结 ====================
平均收益率: 0.9969
平均能量使用: 0.4944
平均推理时间: 0.1696秒
