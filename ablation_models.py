"""
消融实验模型实现
包含GPN+LSTM和PN+IndRNN两个消融模型，用于验证不同组件的有效性
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from gpn import Encoder, GPN, LSTM
from pn import PN4SMP
from indrnn.indrnn import IndRNN, IndRNN_Net, IndRNNv2
from attention.attention import MultiHead_Additive_Attention
from transformer import ConstellationTransformer
from constellation_smp.gpn_constellation import ConstellationEncoder

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class GPNConstellationLSTM(nn.Module):
    """
    消融实验模型1: GPN + LSTM
    将原始GPN+IndRNN中的IndRNN替换为LSTM，验证IndRNN的有效性
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='lstm', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 use_transformer=False, transformer_config=None):
        super(GPNConstellationLSTM, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.use_transformer = use_transformer

        # 星座编码器（复用原有实现）
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer,
            transformer_config
        )
        
        # 任务选择器：使用GPN但强制使用LSTM
        self.task_selector = GPN(
            hidden_size,
            'lstm',  # 强制使用LSTM
            num_nodes,
            dropout=dropout,
            num_layers=num_layers
        )
        
        # 卫星选择器（复用原有实现）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, num_satellites)
        )
        
        # 初始化参数
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_uniform_(m.weight, a=math.sqrt(5))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, static, dynamic):
        """前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 星座编码 - constellation_encoder返回tuple (constellation_features, satellite_features)
        constellation_features, _ = self.constellation_encoder(static, dynamic)

        # 将constellation_features转换为GPN期望的格式
        # constellation_features: (batch_size, hidden_size, seq_len)
        # 转换为: (batch_size, seq_len, hidden_size)
        context = constellation_features.permute(0, 2, 1)

        # 创建初始输入
        x = torch.mean(context, dim=1)  # (batch_size, hidden_size)

        # 任务选择（使用LSTM的GPN）
        # 使用GPN4SMP的接口，它能正确处理static和dynamic
        from gpn import GPN4SMP
        if not hasattr(self, '_temp_gpn4smp'):
            self._temp_gpn4smp = GPN4SMP(
                self.static_size, self.dynamic_size, self.hidden_size,
                'constellation_smp', 'lstm', 2,  # task, rnn, num_layers
                self.update_fn, self.mask_fn, static.size(2), 0.1  # update_fn, mask_fn, num_nodes, dropout
            ).to(static.device)

        # 处理dynamic数据维度
        if len(dynamic.shape) == 4:
            dynamic_2d = dynamic.mean(dim=-1)  # 平均所有卫星的动态信息
        else:
            dynamic_2d = dynamic

        tour_indices, tour_log_prob = self._temp_gpn4smp(static, dynamic_2d)
        
        # 卫星选择
        # 获取选中任务的特征
        selected_task_features = torch.gather(
            constellation_features.permute(0, 2, 1), 1,
            tour_indices.unsqueeze(-1).expand(-1, -1, self.hidden_size)
        )
        
        # 计算卫星选择概率
        satellite_logits = []
        satellite_log_probs = []
        
        for i in range(seq_len):
            if i < selected_task_features.size(1):
                task_feat = selected_task_features[:, i, :]  # (batch_size, hidden_size)
                
                # 结合任务特征和全局特征
                global_feat = torch.mean(constellation_features, dim=2)  # (batch_size, hidden_size)
                combined_feat = torch.cat([task_feat, global_feat], dim=1)  # (batch_size, hidden_size*2)
                
                # 计算卫星选择logits
                sat_logits = self.satellite_selector(combined_feat)  # (batch_size, num_satellites)
                satellite_logits.append(sat_logits)
                
                # 计算log概率
                sat_log_prob = F.log_softmax(sat_logits, dim=1)
                satellite_log_probs.append(sat_log_prob)
        
        if satellite_logits:
            satellite_logits = torch.stack(satellite_logits, dim=1)  # (batch_size, seq_len, num_satellites)
            satellite_log_probs = torch.stack(satellite_log_probs, dim=1)  # (batch_size, seq_len, num_satellites)
            
            # 采样卫星选择
            satellite_probs = F.softmax(satellite_logits, dim=2)
            satellite_indices = torch.multinomial(satellite_probs.view(-1, self.num_satellites), 1).view(batch_size, -1)
            
            # 获取选中卫星的log概率
            satellite_log_prob = torch.gather(satellite_log_probs, 2, satellite_indices.unsqueeze(-1)).squeeze(-1)
        else:
            satellite_indices = torch.zeros(batch_size, seq_len, dtype=torch.long, device=static.device)
            satellite_log_prob = torch.zeros(batch_size, seq_len, device=static.device)
        
        return tour_indices, satellite_indices, tour_log_prob, satellite_log_prob


class PNConstellationIndRNN(nn.Module):
    """
    消融实验模型2: PN + IndRNN
    将原始GPN替换为传统的PN（Pointer Network），验证GPN的有效性
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='indrnn', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 use_transformer=False, transformer_config=None):
        super(PNConstellationIndRNN, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.use_transformer = use_transformer

        # 星座编码器（复用原有实现）
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer,
            transformer_config
        )

        # 任务选择器：创建一个简化的PN，使用IndRNN
        from attention.attention import MultiHead_Additive_Attention
        from indrnn.indrnn import IndRNN_Net, IndRNN

        # 编码器
        self.static_encoder = Encoder(static_size, hidden_size)
        self.dynamic_encoder = Encoder(dynamic_size, hidden_size)
        self.decoder = Encoder(static_size, hidden_size)

        # 指针网络组件
        self.pointer_rnn = IndRNN_Net(hidden_size, hidden_size, num_nodes, num_layers, IndRNN)
        self.pointer_attention = MultiHead_Additive_Attention(hidden_size, n_head=8)
        self.dropout = nn.Dropout(dropout)

        # 初始解码器输入
        self.x0 = torch.zeros((1, static_size, 1), requires_grad=True, device=device)

        # 卫星选择器（复用原有实现）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, num_satellites)
        )

        # 初始化参数
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_uniform_(m.weight, a=math.sqrt(5))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, static, dynamic):
        """前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 星座编码
        constellation_features = self.constellation_encoder(static, dynamic)

        # 任务选择（使用简化的PN）
        tour_indices, tour_log_prob = self._pointer_network_forward(static, dynamic)

        # 卫星选择
        selected_task_features = torch.gather(
            constellation_features.permute(0, 2, 1), 1,
            tour_indices.unsqueeze(-1).expand(-1, -1, self.hidden_size)
        )

        satellite_logits = []
        satellite_log_probs = []

        for i in range(min(seq_len, selected_task_features.size(1))):
            task_feat = selected_task_features[:, i, :]
            global_feat = torch.mean(constellation_features, dim=2)
            combined_feat = torch.cat([task_feat, global_feat], dim=1)

            sat_logits = self.satellite_selector(combined_feat)
            satellite_logits.append(sat_logits)

            sat_log_prob = F.log_softmax(sat_logits, dim=1)
            satellite_log_probs.append(sat_log_prob)

        if satellite_logits:
            satellite_logits = torch.stack(satellite_logits, dim=1)
            satellite_log_probs = torch.stack(satellite_log_probs, dim=1)

            satellite_probs = F.softmax(satellite_logits, dim=2)
            satellite_indices = torch.multinomial(satellite_probs.view(-1, self.num_satellites), 1).view(batch_size, -1)
            satellite_log_prob = torch.gather(satellite_log_probs, 2, satellite_indices.unsqueeze(-1)).squeeze(-1)
        else:
            satellite_indices = torch.zeros(batch_size, seq_len, dtype=torch.long, device=static.device)
            satellite_log_prob = torch.zeros(batch_size, seq_len, device=static.device)

        return tour_indices, satellite_indices, tour_log_prob, satellite_log_prob

    def _pointer_network_forward(self, static, dynamic):
        """简化的指针网络前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 处理dynamic数据的维度 - dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        # 需要将其转换为 (batch_size, dynamic_size, seq_len) 用于编码
        if len(dynamic.shape) == 4:
            # 取第一个卫星的动态信息或者平均所有卫星的信息
            dynamic_for_encoding = dynamic.mean(dim=-1)  # (batch_size, dynamic_size, seq_len)
        else:
            dynamic_for_encoding = dynamic

        # 编码
        static_hidden = self.static_encoder(static)  # (batch_size, hidden_size, seq_len)
        dynamic_hidden = self.dynamic_encoder(dynamic_for_encoding)  # (batch_size, hidden_size, seq_len)

        # 初始解码器输入
        decoder_input = self.x0.expand(batch_size, -1, -1)  # (batch_size, static_size, 1)
        decoder_hidden = self.decoder(decoder_input)  # (batch_size, hidden_size, 1)

        # 创建掩码
        mask = torch.ones(batch_size, seq_len, device=static.device)

        tour_indices = []
        tour_log_probs = []
        last_hh = None

        for step in range(seq_len):
            # 指针网络注意力
            # 将decoder_hidden转换为RNN输入格式
            rnn_input = decoder_hidden.transpose(2, 1)  # (batch_size, 1, hidden_size)
            rnn_out, last_hh = self.pointer_rnn(rnn_input, last_hh)
            rnn_out = self.dropout(rnn_out.squeeze(1))  # (batch_size, hidden_size)

            # 计算注意力概率
            # 合并static和dynamic特征作为context
            combined_hidden = static_hidden + dynamic_hidden  # (batch_size, hidden_size, seq_len)
            # 将context转换为MultiHead_Additive_Attention期望的格式
            context_flat = combined_hidden.permute(0, 2, 1).contiguous().view(-1, self.hidden_size)
            attention_result = self.pointer_attention(rnn_out, context_flat)

            # 处理注意力机制的返回值（可能是tuple）
            if isinstance(attention_result, tuple):
                probs = attention_result[0]  # 取第一个元素作为概率
            else:
                probs = attention_result

            probs = F.softmax(probs + mask.log(), dim=1)

            # 采样
            if self.training:
                m = torch.distributions.Categorical(probs)
                ptr = m.sample()
                logp = m.log_prob(ptr)
            else:
                prob, ptr = torch.max(probs, 1)
                logp = prob.log()

            tour_indices.append(ptr.unsqueeze(1))
            tour_log_probs.append(logp.unsqueeze(1))

            # 更新掩码（简化版本，不进行复杂的动态更新）
            mask.scatter_(1, ptr.unsqueeze(1), 0)

            # 更新解码器输入
            decoder_input = torch.gather(static, 2, ptr.view(-1, 1, 1).expand(-1, static_size, 1))
            decoder_hidden = self.decoder(decoder_input)

        tour_indices = torch.cat(tour_indices, dim=1)  # (batch_size, seq_len)
        tour_log_probs = torch.cat(tour_log_probs, dim=1)  # (batch_size, seq_len)

        return tour_indices, tour_log_probs


def create_ablation_model(ablation_model, static_size, dynamic_size, hidden_size, num_satellites,
                         rnn, num_layers, update_fn, mask_fn, num_nodes, dropout,
                         constellation_mode, use_transformer, transformer_config):
    """
    根据消融实验配置创建对应的模型
    
    Args:
        ablation_model: 消融模型类型 ('gpn_indrnn', 'gpn_lstm', 'pn_indrnn')
        其他参数: 模型构造参数
    
    Returns:
        对应的消融模型实例
    """
    if ablation_model == 'gpn_lstm':
        return GPNConstellationLSTM(
            static_size, dynamic_size, hidden_size, num_satellites,
            'lstm', num_layers, update_fn, mask_fn, num_nodes, dropout,
            constellation_mode, use_transformer, transformer_config
        )
    elif ablation_model == 'pn_indrnn':
        return PNConstellationIndRNN(
            static_size, dynamic_size, hidden_size, num_satellites,
            'indrnn', num_layers, update_fn, mask_fn, num_nodes, dropout,
            constellation_mode, use_transformer, transformer_config
        )
    else:  # 'gpn_indrnn' - 原始模型
        from constellation_smp.gpn_constellation import GPNConstellation
        return GPNConstellation(
            static_size, dynamic_size, hidden_size, num_satellites,
            rnn, num_layers, update_fn, mask_fn, num_nodes, dropout,
            constellation_mode, use_transformer, transformer_config
        )
