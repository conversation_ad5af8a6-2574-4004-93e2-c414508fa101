"""
消融实验模型实现
包含GPN+LSTM和PN+IndRNN两个消融模型，用于验证不同组件的有效性
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from gpn import Encoder, GPN, LSTM
from pn import PN4SMP
from indrnn.indrnn import IndRNN, IndRNN_Net, IndRNNv2
from attention.attention import MultiHead_Additive_Attention
from transformer import ConstellationTransformer
from constellation_smp.gpn_constellation import ConstellationEncoder

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class GPNConstellationLSTM(nn.Module):
    """
    消融实验模型1: GPN + LSTM
    将原始GPN+IndRNN中的IndRNN替换为LSTM，验证IndRNN的有效性
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='lstm', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 use_transformer=False, transformer_config=None):
        super(GPNConstellationLSTM, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.use_transformer = use_transformer

        # 星座编码器（复用原有实现）
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer,
            transformer_config
        )
        
        # 任务选择器：使用GPN但强制使用LSTM
        self.task_selector = GPN(
            hidden_size,
            'lstm',  # 强制使用LSTM
            num_nodes,
            dropout=dropout,
            num_layers=num_layers
        )
        
        # 卫星选择器（复用原有实现）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, num_satellites)
        )
        
        # 初始化参数
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_uniform_(m.weight, a=math.sqrt(5))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, static, dynamic):
        """前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 星座编码 - constellation_encoder返回tuple (constellation_features, satellite_features)
        constellation_features, _ = self.constellation_encoder(static, dynamic)

        # 直接使用constellation_features进行任务选择
        # constellation_features: (batch_size, hidden_size, seq_len)

        # 使用改进的任务选择逻辑，包含资源约束
        tour_indices, tour_log_prob = self._resource_aware_task_selection(constellation_features, static, dynamic)

        # 资源感知的卫星选择
        satellite_indices, satellite_log_prob = self._resource_aware_satellite_selection(
            constellation_features, tour_indices, static, dynamic, seq_len
        )

        return tour_indices, satellite_indices, tour_log_prob, satellite_log_prob

    def _simple_task_selection(self, constellation_features, static, dynamic):
        """简化的任务选择，使用LSTM进行序列决策"""
        batch_size, hidden_size, seq_len = constellation_features.size()

        # 将constellation_features转换为序列格式 (batch_size, seq_len, hidden_size)
        context = constellation_features.permute(0, 2, 1)

        # 使用LSTM进行序列处理
        lstm = nn.LSTM(hidden_size, hidden_size, batch_first=True, dropout=0.1).to(constellation_features.device)
        lstm_out, _ = lstm(context)  # (batch_size, seq_len, hidden_size)

        # 计算任务选择概率
        task_logits = torch.sum(lstm_out * context, dim=2)  # (batch_size, seq_len)
        task_probs = F.softmax(task_logits, dim=1)

        # 采样任务序列
        tour_indices = []
        tour_log_probs = []

        for step in range(seq_len):
            if self.training:
                m = torch.distributions.Categorical(task_probs)
                idx = m.sample()
                log_prob = m.log_prob(idx)
            else:
                prob, idx = torch.max(task_probs, 1)
                log_prob = prob.log()

            tour_indices.append(idx.unsqueeze(1))
            tour_log_probs.append(log_prob.unsqueeze(1))

            # 简单的掩码更新（避免重复选择）
            task_probs = task_probs.scatter(1, idx.unsqueeze(1), 0)
            task_probs = F.softmax(task_probs, dim=1)

        tour_indices = torch.cat(tour_indices, dim=1)  # (batch_size, seq_len)
        tour_log_probs = torch.cat(tour_log_probs, dim=1)  # (batch_size, seq_len)

        return tour_indices, tour_log_probs

    def _resource_aware_task_selection(self, constellation_features, static, dynamic):
        """资源感知的任务选择，使用LSTM进行序列决策"""
        batch_size, hidden_size, seq_len = constellation_features.size()

        # 将constellation_features转换为序列格式 (batch_size, seq_len, hidden_size)
        context = constellation_features.permute(0, 2, 1)

        # 使用LSTM进行序列处理
        lstm = nn.LSTM(hidden_size, hidden_size, batch_first=True, dropout=0.1).to(constellation_features.device)
        lstm_out, _ = lstm(context)  # (batch_size, seq_len, hidden_size)

        # 计算任务选择概率
        task_logits = torch.sum(lstm_out * context, dim=2)  # (batch_size, seq_len)

        # 采样任务序列（带资源约束）
        tour_indices = []
        tour_log_probs = []
        current_dynamic = dynamic.clone()  # 复制动态状态用于更新

        for step in range(seq_len):
            # 获取当前有效任务掩码
            if self.mask_fn:
                mask, satellite_masks = self.mask_fn(current_dynamic, static=static)  # 正确的参数顺序
                if mask is not None:
                    # 应用资源约束掩码
                    masked_logits = task_logits + mask.log()
                    task_probs = F.softmax(masked_logits, dim=1)
                else:
                    task_probs = F.softmax(task_logits, dim=1)
            else:
                task_probs = F.softmax(task_logits, dim=1)

            # 采样任务
            if self.training:
                m = torch.distributions.Categorical(task_probs)
                idx = m.sample()
                log_prob = m.log_prob(idx)
            else:
                prob, idx = torch.max(task_probs, 1)
                log_prob = prob.log()

            tour_indices.append(idx.unsqueeze(1))
            tour_log_probs.append(log_prob.unsqueeze(1))

            # 更新任务选择掩码（避免重复选择）
            task_logits = task_logits.scatter(1, idx.unsqueeze(1), float('-inf'))

            # 更新动态状态（模拟任务执行的资源消耗）
            if self.update_fn and step < seq_len - 1:  # 最后一步不需要更新
                # 创建临时的卫星选择（用于状态更新）
                temp_satellite_idx = torch.zeros_like(idx)  # 简化：选择第一个卫星
                # 正确的update_fn调用：static, dynamic, chosen_idx, satellite_idx
                current_dynamic = self.update_fn(static, current_dynamic, idx, temp_satellite_idx)

        tour_indices = torch.cat(tour_indices, dim=1)  # (batch_size, seq_len)
        tour_log_probs = torch.cat(tour_log_probs, dim=1)  # (batch_size, seq_len)

        return tour_indices, tour_log_probs

    def _resource_aware_satellite_selection(self, constellation_features, tour_indices, static, dynamic, seq_len):
        """资源感知的卫星选择"""
        batch_size = constellation_features.size(0)

        # 获取选中任务的特征
        selected_task_features = torch.gather(
            constellation_features.permute(0, 2, 1), 1,
            tour_indices.unsqueeze(-1).expand(-1, -1, self.hidden_size)
        )

        satellite_indices = []
        satellite_log_probs = []
        current_dynamic = dynamic.clone()

        for i in range(seq_len):
            if i < selected_task_features.size(1):
                task_feat = selected_task_features[:, i, :]  # (batch_size, hidden_size)

                # 结合任务特征和全局特征
                global_feat = torch.mean(constellation_features, dim=2)  # (batch_size, hidden_size)
                combined_feat = torch.cat([task_feat, global_feat], dim=1)  # (batch_size, hidden_size*2)

                # 计算卫星选择logits
                sat_logits = self.satellite_selector(combined_feat)  # (batch_size, num_satellites)

                # 应用卫星资源约束掩码
                if self.mask_fn:
                    # 获取当前任务的卫星可用性掩码
                    current_task = tour_indices[:, i].unsqueeze(1)  # (batch_size, 1)
                    sat_mask = self._get_satellite_mask(current_dynamic, current_task)  # (batch_size, num_satellites)
                    if sat_mask is not None:
                        sat_logits = sat_logits + sat_mask.log()

                # 采样卫星
                sat_probs = F.softmax(sat_logits, dim=1)
                if self.training:
                    m = torch.distributions.Categorical(sat_probs)
                    sat_idx = m.sample()
                    sat_log_prob = m.log_prob(sat_idx)
                else:
                    prob, sat_idx = torch.max(sat_probs, 1)
                    sat_log_prob = prob.log()

                satellite_indices.append(sat_idx.unsqueeze(1))
                satellite_log_probs.append(sat_log_prob.unsqueeze(1))

                # 更新动态状态
                if self.update_fn and i < seq_len - 1:
                    # 正确的update_fn调用：static, dynamic, chosen_idx, satellite_idx
                    current_dynamic = self.update_fn(static, current_dynamic, current_task.squeeze(1), sat_idx)
            else:
                # 填充零值
                satellite_indices.append(torch.zeros(batch_size, 1, dtype=torch.long, device=constellation_features.device))
                satellite_log_probs.append(torch.zeros(batch_size, 1, device=constellation_features.device))

        satellite_indices = torch.cat(satellite_indices, dim=1)  # (batch_size, seq_len)
        satellite_log_prob = torch.cat(satellite_log_probs, dim=1)  # (batch_size, seq_len)

        return satellite_indices, satellite_log_prob

    def _get_satellite_mask(self, dynamic, task_indices):
        """获取卫星资源约束掩码"""
        batch_size = dynamic.size(0)
        num_satellites = self.num_satellites

        # 创建基础掩码（所有卫星都可用）
        mask = torch.ones(batch_size, num_satellites, device=dynamic.device)

        # 检查每个卫星的资源约束
        # dynamic shape: (batch_size, dynamic_size, seq_len, num_satellites)
        if len(dynamic.shape) == 4:
            # 获取当前任务的资源需求（简化处理）
            for sat_idx in range(num_satellites):
                # 检查内存约束（假设dynamic的第0维是内存）
                available_memory = dynamic[:, 0, :, sat_idx].sum(dim=1)  # (batch_size,)
                memory_threshold = 0.1  # 最小内存阈值

                # 检查功耗约束（假设dynamic的第1维是功耗）
                available_power = dynamic[:, 1, :, sat_idx].sum(dim=1)  # (batch_size,)
                power_threshold = 0.1  # 最小功耗阈值

                # 更新掩码
                insufficient_resources = (available_memory < memory_threshold) | (available_power < power_threshold)
                mask[:, sat_idx] = mask[:, sat_idx] * (~insufficient_resources).float()

        # 确保至少有一个卫星可用
        if mask.sum(dim=1).min() == 0:
            # 如果所有卫星都不可用，强制第一个卫星可用
            mask[:, 0] = 1.0

        return mask


class PNConstellationIndRNN(nn.Module):
    """
    消融实验模型2: PN + IndRNN
    将原始GPN替换为传统的PN（Pointer Network），验证GPN的有效性
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='indrnn', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 use_transformer=False, transformer_config=None):
        super(PNConstellationIndRNN, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.use_transformer = use_transformer

        # 星座编码器（复用原有实现）
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer,
            transformer_config
        )

        # 任务选择器：创建一个简化的PN，使用IndRNN
        from indrnn.indrnn import IndRNN_Net, IndRNN

        # 编码器
        self.static_encoder = Encoder(static_size, hidden_size)
        self.dynamic_encoder = Encoder(dynamic_size, hidden_size)

        # 指针网络组件
        self.pointer_rnn = IndRNN_Net(hidden_size, hidden_size, num_nodes, num_layers, IndRNN)
        self.dropout = nn.Dropout(dropout)

        # 简化的注意力机制
        self.attention_linear = nn.Linear(hidden_size * 2, hidden_size)
        self.attention_v = nn.Linear(hidden_size, 1)

        # 初始解码器输入
        self.x0 = torch.zeros((1, static_size, 1), requires_grad=True, device=device)

        # 卫星选择器（复用原有实现）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, num_satellites)
        )

        # 初始化参数
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_uniform_(m.weight, a=math.sqrt(5))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, static, dynamic):
        """前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 星座编码 - constellation_encoder返回tuple (constellation_features, satellite_features)
        constellation_features, _ = self.constellation_encoder(static, dynamic)

        # 资源感知的任务选择（使用改进的PN）
        tour_indices, tour_log_prob = self._resource_aware_pointer_network_forward(static, dynamic)

        # 资源感知的卫星选择
        satellite_indices, satellite_log_prob = self._resource_aware_satellite_selection(
            constellation_features, tour_indices, static, dynamic, seq_len
        )

        return tour_indices, satellite_indices, tour_log_prob, satellite_log_prob

    def _pointer_network_forward(self, static, dynamic):
        """简化的指针网络前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 处理dynamic数据的维度
        if len(dynamic.shape) == 4:
            dynamic_for_encoding = dynamic.mean(dim=-1)  # (batch_size, dynamic_size, seq_len)
        else:
            dynamic_for_encoding = dynamic

        # 编码
        static_hidden = self.static_encoder(static)  # (batch_size, hidden_size, seq_len)
        dynamic_hidden = self.dynamic_encoder(dynamic_for_encoding)  # (batch_size, hidden_size, seq_len)

        # 合并特征
        combined_hidden = static_hidden + dynamic_hidden  # (batch_size, hidden_size, seq_len)

        # 转换为序列格式进行IndRNN处理
        sequence_input = combined_hidden.permute(0, 2, 1)  # (batch_size, seq_len, hidden_size)

        # 使用IndRNN处理序列
        rnn_output, _ = self.pointer_rnn(sequence_input, None)  # (batch_size, seq_len, hidden_size)

        # 计算任务选择概率
        # 简化的注意力机制
        attention_input = torch.cat([sequence_input, rnn_output], dim=2)  # (batch_size, seq_len, 2*hidden_size)
        attention_weights = self.attention_linear(attention_input)  # (batch_size, seq_len, hidden_size)
        attention_weights = torch.tanh(attention_weights)
        task_logits = self.attention_v(attention_weights).squeeze(2)  # (batch_size, seq_len)

        # 创建掩码
        mask = torch.ones(batch_size, seq_len, device=static.device)

        tour_indices = []
        tour_log_probs = []

        for step in range(seq_len):
            # 应用掩码
            masked_logits = task_logits + mask.log()
            probs = F.softmax(masked_logits, dim=1)

            # 采样
            if self.training:
                m = torch.distributions.Categorical(probs)
                ptr = m.sample()
                logp = m.log_prob(ptr)
            else:
                prob, ptr = torch.max(probs, 1)
                logp = prob.log()

            tour_indices.append(ptr.unsqueeze(1))
            tour_log_probs.append(logp.unsqueeze(1))

            # 更新掩码
            mask.scatter_(1, ptr.unsqueeze(1), 0)

        tour_indices = torch.cat(tour_indices, dim=1)  # (batch_size, seq_len)
        tour_log_probs = torch.cat(tour_log_probs, dim=1)  # (batch_size, seq_len)

        return tour_indices, tour_log_probs

    def _resource_aware_pointer_network_forward(self, static, dynamic):
        """资源感知的指针网络前向传播"""
        batch_size, static_size, seq_len = static.size()

        # 处理dynamic数据的维度
        if len(dynamic.shape) == 4:
            dynamic_for_encoding = dynamic.mean(dim=-1)  # (batch_size, dynamic_size, seq_len)
        else:
            dynamic_for_encoding = dynamic

        # 编码
        static_hidden = self.static_encoder(static)  # (batch_size, hidden_size, seq_len)
        dynamic_hidden = self.dynamic_encoder(dynamic_for_encoding)  # (batch_size, hidden_size, seq_len)

        # 合并特征
        combined_hidden = static_hidden + dynamic_hidden  # (batch_size, hidden_size, seq_len)

        # 转换为序列格式进行IndRNN处理
        sequence_input = combined_hidden.permute(0, 2, 1)  # (batch_size, seq_len, hidden_size)

        # 使用IndRNN处理序列
        rnn_output, _ = self.pointer_rnn(sequence_input, None)  # (batch_size, seq_len, hidden_size)

        # 计算任务选择概率
        # 简化的注意力机制
        attention_input = torch.cat([sequence_input, rnn_output], dim=2)  # (batch_size, seq_len, 2*hidden_size)
        attention_weights = self.attention_linear(attention_input)  # (batch_size, seq_len, hidden_size)
        attention_weights = torch.tanh(attention_weights)
        task_logits = self.attention_v(attention_weights).squeeze(2)  # (batch_size, seq_len)

        # 采样任务序列（带资源约束）
        tour_indices = []
        tour_log_probs = []
        current_dynamic = dynamic.clone()  # 复制动态状态用于更新

        for step in range(seq_len):
            # 获取当前有效任务掩码
            if self.mask_fn:
                mask, satellite_masks = self.mask_fn(current_dynamic, static=static)  # 正确的参数顺序
                if mask is not None:
                    # 应用资源约束掩码
                    masked_logits = task_logits + mask.log()
                    probs = F.softmax(masked_logits, dim=1)
                else:
                    probs = F.softmax(task_logits, dim=1)
            else:
                probs = F.softmax(task_logits, dim=1)

            # 采样
            if self.training:
                m = torch.distributions.Categorical(probs)
                ptr = m.sample()
                logp = m.log_prob(ptr)
            else:
                prob, ptr = torch.max(probs, 1)
                logp = prob.log()

            tour_indices.append(ptr.unsqueeze(1))
            tour_log_probs.append(logp.unsqueeze(1))

            # 更新任务选择掩码（避免重复选择）
            task_logits = task_logits.scatter(1, ptr.unsqueeze(1), float('-inf'))

            # 更新动态状态（模拟任务执行的资源消耗）
            if self.update_fn and step < seq_len - 1:  # 最后一步不需要更新
                # 创建临时的卫星选择（用于状态更新）
                temp_satellite_idx = torch.zeros_like(ptr)  # 简化：选择第一个卫星
                # 正确的update_fn调用：static, dynamic, chosen_idx, satellite_idx
                current_dynamic = self.update_fn(static, current_dynamic, ptr, temp_satellite_idx)

        tour_indices = torch.cat(tour_indices, dim=1)  # (batch_size, seq_len)
        tour_log_probs = torch.cat(tour_log_probs, dim=1)  # (batch_size, seq_len)

        return tour_indices, tour_log_probs

    def _resource_aware_satellite_selection(self, constellation_features, tour_indices, static, dynamic, seq_len):
        """资源感知的卫星选择（PN+IndRNN版本）"""
        batch_size = constellation_features.size(0)

        # 获取选中任务的特征
        selected_task_features = torch.gather(
            constellation_features.permute(0, 2, 1), 1,
            tour_indices.unsqueeze(-1).expand(-1, -1, self.hidden_size)
        )

        satellite_indices = []
        satellite_log_probs = []
        current_dynamic = dynamic.clone()

        for i in range(seq_len):
            if i < selected_task_features.size(1):
                task_feat = selected_task_features[:, i, :]  # (batch_size, hidden_size)

                # 结合任务特征和全局特征
                global_feat = torch.mean(constellation_features, dim=2)  # (batch_size, hidden_size)
                combined_feat = torch.cat([task_feat, global_feat], dim=1)  # (batch_size, hidden_size*2)

                # 计算卫星选择logits
                sat_logits = self.satellite_selector(combined_feat)  # (batch_size, num_satellites)

                # 应用卫星资源约束掩码
                if self.mask_fn:
                    # 获取当前任务的卫星可用性掩码
                    current_task = tour_indices[:, i].unsqueeze(1)  # (batch_size, 1)
                    sat_mask = self._get_satellite_mask(current_dynamic, current_task)  # (batch_size, num_satellites)
                    if sat_mask is not None:
                        sat_logits = sat_logits + sat_mask.log()

                # 采样卫星
                sat_probs = F.softmax(sat_logits, dim=1)
                if self.training:
                    m = torch.distributions.Categorical(sat_probs)
                    sat_idx = m.sample()
                    sat_log_prob = m.log_prob(sat_idx)
                else:
                    prob, sat_idx = torch.max(sat_probs, 1)
                    sat_log_prob = prob.log()

                satellite_indices.append(sat_idx.unsqueeze(1))
                satellite_log_probs.append(sat_log_prob.unsqueeze(1))

                # 更新动态状态
                if self.update_fn and i < seq_len - 1:
                    # 正确的update_fn调用：static, dynamic, chosen_idx, satellite_idx
                    current_dynamic = self.update_fn(static, current_dynamic, current_task.squeeze(1), sat_idx)
            else:
                # 填充零值
                satellite_indices.append(torch.zeros(batch_size, 1, dtype=torch.long, device=constellation_features.device))
                satellite_log_probs.append(torch.zeros(batch_size, 1, device=constellation_features.device))

        satellite_indices = torch.cat(satellite_indices, dim=1)  # (batch_size, seq_len)
        satellite_log_prob = torch.cat(satellite_log_probs, dim=1)  # (batch_size, seq_len)

        return satellite_indices, satellite_log_prob

    def _get_satellite_mask(self, dynamic, task_indices):
        """获取卫星资源约束掩码（PN+IndRNN版本）"""
        batch_size = dynamic.size(0)
        num_satellites = self.num_satellites

        # 创建基础掩码（所有卫星都可用）
        mask = torch.ones(batch_size, num_satellites, device=dynamic.device)

        # 检查每个卫星的资源约束
        # dynamic shape: (batch_size, dynamic_size, seq_len, num_satellites)
        if len(dynamic.shape) == 4:
            # 获取当前任务的资源需求（简化处理）
            for sat_idx in range(num_satellites):
                # 检查内存约束（假设dynamic的第0维是内存）
                available_memory = dynamic[:, 0, :, sat_idx].sum(dim=1)  # (batch_size,)
                memory_threshold = 0.1  # 最小内存阈值

                # 检查功耗约束（假设dynamic的第1维是功耗）
                available_power = dynamic[:, 1, :, sat_idx].sum(dim=1)  # (batch_size,)
                power_threshold = 0.1  # 最小功耗阈值

                # 更新掩码
                insufficient_resources = (available_memory < memory_threshold) | (available_power < power_threshold)
                mask[:, sat_idx] = mask[:, sat_idx] * (~insufficient_resources).float()

        # 确保至少有一个卫星可用
        if mask.sum(dim=1).min() == 0:
            # 如果所有卫星都不可用，强制第一个卫星可用
            mask[:, 0] = 1.0

        return mask


def create_ablation_model(ablation_model, static_size, dynamic_size, hidden_size, num_satellites,
                         rnn, num_layers, update_fn, mask_fn, num_nodes, dropout,
                         constellation_mode, use_transformer, transformer_config):
    """
    根据消融实验配置创建对应的模型
    
    Args:
        ablation_model: 消融模型类型 ('gpn_indrnn', 'gpn_lstm', 'pn_indrnn')
        其他参数: 模型构造参数
    
    Returns:
        对应的消融模型实例
    """
    if ablation_model == 'gpn_lstm':
        return GPNConstellationLSTM(
            static_size, dynamic_size, hidden_size, num_satellites,
            'lstm', num_layers, update_fn, mask_fn, num_nodes, dropout,
            constellation_mode, use_transformer, transformer_config
        )
    elif ablation_model == 'pn_indrnn':
        return PNConstellationIndRNN(
            static_size, dynamic_size, hidden_size, num_satellites,
            'indrnn', num_layers, update_fn, mask_fn, num_nodes, dropout,
            constellation_mode, use_transformer, transformer_config
        )
    else:  # 'gpn_indrnn' - 原始模型
        from constellation_smp.gpn_constellation import GPNConstellation
        return GPNConstellation(
            static_size, dynamic_size, hidden_size, num_satellites,
            rnn, num_layers, update_fn, mask_fn, num_nodes, dropout,
            constellation_mode, use_transformer, transformer_config
        )
