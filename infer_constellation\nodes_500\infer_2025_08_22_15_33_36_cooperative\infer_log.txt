推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/ablation_pn_indrnn_comparison_transformer_L2H4_2025_08_22_13_43_33/constellation_pn_indrnn_cooperative_transformer_L2H4_2025_08_22_13_43_33

批次 1:
  奖励值: 191.5319
  收益率: 0.9996
  距离: 79.1505
  内存使用: 2.1736
  能量使用: 2.4121
  推理时间: 0.6832秒

批次 2:
  奖励值: 188.1510
  收益率: 0.9996
  距离: 81.6512
  内存使用: 2.3457
  能量使用: 2.5011
  推理时间: 0.5586秒

批次 3:
  奖励值: 184.3884
  收益率: 0.9996
  距离: 82.2485
  内存使用: 2.2270
  能量使用: 2.5476
  推理时间: 0.6644秒

批次 4:
  奖励值: 185.8330
  收益率: 0.9996
  距离: 79.7494
  内存使用: 2.1203
  能量使用: 2.4642
  推理时间: 0.6098秒

批次 5:
  奖励值: 177.2571
  收益率: 0.9996
  距离: 88.1220
  内存使用: 2.1910
  能量使用: 2.5727
  推理时间: 0.6838秒

批次 6:
  奖励值: 175.6205
  收益率: 0.9995
  距离: 87.1596
  内存使用: 2.2657
  能量使用: 2.5338
  推理时间: 0.5734秒

批次 7:
  奖励值: 182.4581
  收益率: 0.9996
  距离: 79.3044
  内存使用: 2.1634
  能量使用: 2.6161
  推理时间: 0.5667秒

批次 8:
  奖励值: 177.3711
  收益率: 0.9995
  距离: 83.4071
  内存使用: 2.2958
  能量使用: 2.5550
  推理时间: 0.5716秒

批次 9:
  奖励值: 174.6934
  收益率: 0.9995
  距离: 80.7352
  内存使用: 2.2683
  能量使用: 2.6096
  推理时间: 0.6071秒

批次 10:
  奖励值: 185.7934
  收益率: 0.9996
  距离: 85.1863
  内存使用: 2.2093
  能量使用: 2.4177
  推理时间: 0.5677秒

批次 11:
  奖励值: 191.7710
  收益率: 0.9996
  距离: 77.3711
  内存使用: 2.1416
  能量使用: 2.5456
  推理时间: 0.6471秒

批次 12:
  奖励值: 183.8221
  收益率: 0.9996
  距离: 83.4715
  内存使用: 2.2190
  能量使用: 2.4653
  推理时间: 0.5682秒

批次 13:
  奖励值: 174.8460
  收益率: 0.9995
  距离: 83.6202
  内存使用: 2.1578
  能量使用: 2.5301
  推理时间: 0.5731秒

批次 14:
  奖励值: 185.8827
  收益率: 0.9996
  距离: 81.0025
  内存使用: 2.3353
  能量使用: 2.4970
  推理时间: 0.6752秒

批次 15:
  奖励值: 184.2392
  收益率: 0.9996
  距离: 82.9783
  内存使用: 2.1055
  能量使用: 2.4378
  推理时间: 0.5788秒

批次 16:
  奖励值: 182.0357
  收益率: 0.9996
  距离: 79.9112
  内存使用: 2.2028
  能量使用: 2.4147
  推理时间: 0.6403秒

批次 17:
  奖励值: 182.7886
  收益率: 0.9991
  距离: 86.6750
  内存使用: 2.1276
  能量使用: 2.4203
  推理时间: 0.6070秒

批次 18:
  奖励值: 173.1702
  收益率: 0.9995
  距离: 85.0332
  内存使用: 2.1143
  能量使用: 2.5120
  推理时间: 0.5637秒

批次 19:
  奖励值: 187.8903
  收益率: 0.9996
  距离: 84.7304
  内存使用: 2.1885
  能量使用: 2.5004
  推理时间: 0.6622秒

批次 20:
  奖励值: 184.0261
  收益率: 0.9991
  距离: 78.1703
  内存使用: 2.2344
  能量使用: 2.3431
  推理时间: 0.6051秒

批次 21:
  奖励值: 182.0333
  收益率: 0.9996
  距离: 84.0111
  内存使用: 2.2170
  能量使用: 2.5054
  推理时间: 0.6744秒

批次 22:
  奖励值: 175.4930
  收益率: 0.9995
  距离: 85.4734
  内存使用: 2.2222
  能量使用: 2.5184
  推理时间: 0.5636秒

批次 23:
  奖励值: 183.2487
  收益率: 0.9996
  距离: 87.2811
  内存使用: 2.2955
  能量使用: 2.5261
  推理时间: 0.5624秒

批次 24:
  奖励值: 174.5996
  收益率: 0.9995
  距离: 81.9430
  内存使用: 2.0601
  能量使用: 2.5974
  推理时间: 0.6299秒

批次 25:
  奖励值: 188.2928
  收益率: 0.9991
  距离: 77.9505
  内存使用: 2.2081
  能量使用: 2.4558
  推理时间: 0.6150秒

批次 26:
  奖励值: 183.1296
  收益率: 0.9991
  距离: 85.1595
  内存使用: 2.3347
  能量使用: 2.4467
  推理时间: 0.5686秒

批次 27:
  奖励值: 182.2473
  收益率: 0.9996
  距离: 83.1433
  内存使用: 2.3121
  能量使用: 2.4501
  推理时间: 0.5658秒

批次 28:
  奖励值: 184.4929
  收益率: 0.9996
  距离: 80.3723
  内存使用: 2.1092
  能量使用: 2.5326
  推理时间: 0.5766秒

批次 29:
  奖励值: 181.7872
  收益率: 0.9996
  距离: 83.3704
  内存使用: 2.0920
  能量使用: 2.5632
  推理时间: 0.6073秒

批次 30:
  奖励值: 179.9690
  收益率: 0.9996
  距离: 86.7997
  内存使用: 2.1473
  能量使用: 2.5150
  推理时间: 0.5656秒

批次 31:
  奖励值: 185.7641
  收益率: 0.9996
  距离: 77.7226
  内存使用: 2.1900
  能量使用: 2.5592
  推理时间: 0.5648秒

批次 32:
  奖励值: 182.9688
  收益率: 0.9987
  距离: 82.4147
  内存使用: 2.1234
  能量使用: 2.5245
  推理时间: 0.5622秒

批次 33:
  奖励值: 185.1977
  收益率: 0.9996
  距离: 81.4381
  内存使用: 2.1209
  能量使用: 2.4456
  推理时间: 0.5799秒

批次 34:
  奖励值: 180.9408
  收益率: 0.9996
  距离: 83.4425
  内存使用: 2.2366
  能量使用: 2.4394
  推理时间: 0.6244秒

批次 35:
  奖励值: 177.7760
  收益率: 0.9991
  距离: 81.2062
  内存使用: 2.1929
  能量使用: 2.4488
  推理时间: 0.6091秒

批次 36:
  奖励值: 186.4299
  收益率: 0.9996
  距离: 81.7970
  内存使用: 2.1740
  能量使用: 2.5691
  推理时间: 0.5695秒

批次 37:
  奖励值: 184.1424
  收益率: 0.9996
  距离: 78.9711
  内存使用: 2.2354
  能量使用: 2.5085
  推理时间: 0.5684秒

批次 38:
  奖励值: 183.0954
  收益率: 0.9991
  距离: 84.4939
  内存使用: 2.2809
  能量使用: 2.4344
  推理时间: 0.6873秒

批次 39:
  奖励值: 187.9346
  收益率: 0.9996
  距离: 79.6451
  内存使用: 2.1989
  能量使用: 2.4866
  推理时间: 0.6221秒

批次 40:
  奖励值: 170.4485
  收益率: 0.9995
  距离: 85.2793
  内存使用: 2.2458
  能量使用: 2.5778
  推理时间: 0.7134秒

批次 41:
  奖励值: 179.2151
  收益率: 0.9996
  距离: 89.3500
  内存使用: 2.2333
  能量使用: 2.3865
  推理时间: 0.5725秒

批次 42:
  奖励值: 184.0500
  收益率: 0.9996
  距离: 85.2056
  内存使用: 2.1593
  能量使用: 2.5351
  推理时间: 0.5673秒

批次 43:
  奖励值: 189.4984
  收益率: 0.9996
  距离: 77.2847
  内存使用: 2.1588
  能量使用: 2.5598
  推理时间: 0.5656秒

批次 44:
  奖励值: 180.5949
  收益率: 0.9996
  距离: 84.1335
  内存使用: 2.2236
  能量使用: 2.4533
  推理时间: 0.5688秒

批次 45:
  奖励值: 191.2684
  收益率: 0.9996
  距离: 88.2797
  内存使用: 2.3139
  能量使用: 2.4695
  推理时间: 0.6184秒

批次 46:
  奖励值: 183.0206
  收益率: 0.9991
  距离: 84.3053
  内存使用: 2.1474
  能量使用: 2.5062
  推理时间: 0.6836秒

批次 47:
  奖励值: 181.0725
  收益率: 0.9996
  距离: 77.6660
  内存使用: 2.1187
  能量使用: 2.4703
  推理时间: 0.6201秒

批次 48:
  奖励值: 178.8479
  收益率: 0.9996
  距离: 90.4333
  内存使用: 2.2082
  能量使用: 2.4627
  推理时间: 0.5698秒

批次 49:
  奖励值: 186.2438
  收益率: 0.9991
  距离: 83.5330
  内存使用: 2.2029
  能量使用: 2.3764
  推理时间: 0.5755秒

批次 50:
  奖励值: 188.3940
  收益率: 0.9996
  距离: 82.1679
  内存使用: 2.1528
  能量使用: 2.4915
  推理时间: 0.6563秒

批次 51:
  奖励值: 176.0614
  收益率: 0.9991
  距离: 87.0830
  内存使用: 2.1898
  能量使用: 2.4043
  推理时间: 0.5693秒

批次 52:
  奖励值: 181.7232
  收益率: 0.9996
  距离: 79.8013
  内存使用: 2.2514
  能量使用: 2.5008
  推理时间: 0.5633秒

批次 53:
  奖励值: 179.7484
  收益率: 0.9996
  距离: 85.2535
  内存使用: 2.1373
  能量使用: 2.5123
  推理时间: 0.6147秒

批次 54:
  奖励值: 188.6356
  收益率: 0.9996
  距离: 79.9267
  内存使用: 2.1866
  能量使用: 2.4155
  推理时间: 0.5640秒

批次 55:
  奖励值: 183.7861
  收益率: 0.9996
  距离: 79.7841
  内存使用: 2.1328
  能量使用: 2.5110
  推理时间: 0.5680秒

批次 56:
  奖励值: 183.6026
  收益率: 0.9996
  距离: 83.3248
  内存使用: 2.1785
  能量使用: 2.4914
  推理时间: 0.5609秒

批次 57:
  奖励值: 181.0356
  收益率: 0.9996
  距离: 86.2957
  内存使用: 2.1587
  能量使用: 2.4743
  推理时间: 0.5644秒

批次 58:
  奖励值: 185.1313
  收益率: 0.9996
  距离: 84.8877
  内存使用: 2.1665
  能量使用: 2.4832
  推理时间: 0.5724秒

批次 59:
  奖励值: 180.2385
  收益率: 0.9996
  距离: 84.8395
  内存使用: 2.1931
  能量使用: 2.4903
  推理时间: 0.6276秒

批次 60:
  奖励值: 188.5730
  收益率: 0.9991
  距离: 79.2366
  内存使用: 2.1910
  能量使用: 2.4266
  推理时间: 0.5727秒

批次 61:
  奖励值: 182.0776
  收益率: 0.9996
  距离: 85.7131
  内存使用: 2.0794
  能量使用: 2.4523
  推理时间: 0.6181秒

批次 62:
  奖励值: 177.9900
  收益率: 0.9995
  距离: 80.9203
  内存使用: 2.1351
  能量使用: 2.5647
  推理时间: 0.5762秒

批次 63:
  奖励值: 190.1800
  收益率: 0.9996
  距离: 86.7137
  内存使用: 2.3678
  能量使用: 2.5587
  推理时间: 0.5790秒

批次 64:
  奖励值: 183.9493
  收益率: 0.9991
  距离: 81.5923
  内存使用: 2.0588
  能量使用: 2.4504
  推理时间: 0.5701秒

批次 65:
  奖励值: 185.6233
  收益率: 0.9996
  距离: 79.6157
  内存使用: 2.2899
  能量使用: 2.4479
  推理时间: 0.5759秒

批次 66:
  奖励值: 182.4701
  收益率: 0.9991
  距离: 82.5818
  内存使用: 2.1981
  能量使用: 2.4798
  推理时间: 0.5727秒

批次 67:
  奖励值: 185.5759
  收益率: 0.9996
  距离: 84.1590
  内存使用: 2.1454
  能量使用: 2.5437
  推理时间: 0.5654秒

批次 68:
  奖励值: 185.4379
  收益率: 0.9996
  距离: 84.1400
  内存使用: 2.1925
  能量使用: 2.3915
  推理时间: 0.5698秒

批次 69:
  奖励值: 183.9048
  收益率: 0.9996
  距离: 73.1064
  内存使用: 2.2617
  能量使用: 2.6221
  推理时间: 0.5689秒

批次 70:
  奖励值: 183.8526
  收益率: 0.9996
  距离: 86.3671
  内存使用: 2.2281
  能量使用: 2.4995
  推理时间: 0.5990秒

批次 71:
  奖励值: 181.8070
  收益率: 0.9996
  距离: 90.4242
  内存使用: 2.2517
  能量使用: 2.5101
  推理时间: 0.5699秒

批次 72:
  奖励值: 166.9248
  收益率: 0.9995
  距离: 84.2328
  内存使用: 2.1507
  能量使用: 2.3669
  推理时间: 0.5705秒

批次 73:
  奖励值: 180.4544
  收益率: 0.9991
  距离: 86.5133
  内存使用: 2.2239
  能量使用: 2.3538
  推理时间: 0.5743秒

批次 74:
  奖励值: 186.5115
  收益率: 0.9996
  距离: 83.0529
  内存使用: 2.1664
  能量使用: 2.3579
  推理时间: 0.5698秒

批次 75:
  奖励值: 188.3291
  收益率: 0.9996
  距离: 77.0454
  内存使用: 2.1560
  能量使用: 2.5405
  推理时间: 0.5681秒

批次 76:
  奖励值: 183.3163
  收益率: 0.9996
  距离: 80.2911
  内存使用: 2.2735
  能量使用: 2.6028
  推理时间: 0.5758秒

批次 77:
  奖励值: 180.3572
  收益率: 0.9996
  距离: 86.3160
  内存使用: 2.0971
  能量使用: 2.4724
  推理时间: 0.5639秒

批次 78:
  奖励值: 192.8440
  收益率: 0.9996
  距离: 83.5529
  内存使用: 2.2274
  能量使用: 2.5316
  推理时间: 0.6119秒

批次 79:
  奖励值: 177.7937
  收益率: 0.9995
  距离: 83.3748
  内存使用: 2.1113
  能量使用: 2.5264
  推理时间: 0.5642秒

批次 80:
  奖励值: 173.4117
  收益率: 0.9995
  距离: 79.1371
  内存使用: 2.2563
  能量使用: 2.3831
  推理时间: 0.5640秒

批次 81:
  奖励值: 184.5984
  收益率: 0.9996
  距离: 86.7910
  内存使用: 2.1013
  能量使用: 2.5108
  推理时间: 0.5645秒

批次 82:
  奖励值: 183.2397
  收益率: 0.9996
  距离: 86.2636
  内存使用: 2.1438
  能量使用: 2.5132
  推理时间: 0.5643秒

批次 83:
  奖励值: 184.7223
  收益率: 0.9996
  距离: 85.5243
  内存使用: 2.0927
  能量使用: 2.5384
  推理时间: 0.5721秒

批次 84:
  奖励值: 181.4502
  收益率: 0.9996
  距离: 86.6523
  内存使用: 2.1553
  能量使用: 2.4919
  推理时间: 0.6672秒

批次 85:
  奖励值: 186.7749
  收益率: 0.9996
  距离: 74.9015
  内存使用: 2.1030
  能量使用: 2.4456
  推理时间: 0.5686秒

批次 86:
  奖励值: 182.7700
  收益率: 0.9996
  距离: 88.7228
  内存使用: 2.1953
  能量使用: 2.5420
  推理时间: 0.5774秒

批次 87:
  奖励值: 178.3670
  收益率: 0.9995
  距离: 77.0051
  内存使用: 2.4044
  能量使用: 2.4564
  推理时间: 0.5658秒

批次 88:
  奖励值: 174.7147
  收益率: 0.9995
  距离: 83.0606
  内存使用: 2.1835
  能量使用: 2.5266
  推理时间: 0.6063秒

批次 89:
  奖励值: 178.3039
  收益率: 0.9996
  距离: 84.1780
  内存使用: 2.1183
  能量使用: 2.4959
  推理时间: 0.6813秒

批次 90:
  奖励值: 179.1455
  收益率: 0.9996
  距离: 83.7199
  内存使用: 2.2733
  能量使用: 2.5156
  推理时间: 0.5720秒

批次 91:
  奖励值: 184.9527
  收益率: 0.9996
  距离: 80.4616
  内存使用: 2.1734
  能量使用: 2.6595
  推理时间: 0.5686秒

批次 92:
  奖励值: 172.2519
  收益率: 0.9995
  距离: 88.1981
  内存使用: 2.1259
  能量使用: 2.3721
  推理时间: 0.5648秒

批次 93:
  奖励值: 180.9704
  收益率: 0.9996
  距离: 81.8212
  内存使用: 2.0889
  能量使用: 2.5492
  推理时间: 0.5620秒

批次 94:
  奖励值: 168.8857
  收益率: 0.9995
  距离: 86.8645
  内存使用: 2.1150
  能量使用: 2.4491
  推理时间: 0.5596秒

批次 95:
  奖励值: 185.3848
  收益率: 0.9996
  距离: 81.0201
  内存使用: 2.1798
  能量使用: 2.4307
  推理时间: 0.5793秒

批次 96:
  奖励值: 176.9091
  收益率: 0.9995
  距离: 82.7415
  内存使用: 2.0808
  能量使用: 2.5594
  推理时间: 0.6136秒

批次 97:
  奖励值: 186.1108
  收益率: 0.9996
  距离: 83.0492
  内存使用: 2.2604
  能量使用: 2.4689
  推理时间: 0.5747秒

批次 98:
  奖励值: 170.4923
  收益率: 0.9995
  距离: 84.8297
  内存使用: 2.1730
  能量使用: 2.4127
  推理时间: 0.5643秒

批次 99:
  奖励值: 184.9485
  收益率: 0.9996
  距离: 82.8676
  内存使用: 2.1230
  能量使用: 2.5124
  推理时间: 0.5646秒

批次 100:
  奖励值: 188.1088
  收益率: 0.9996
  距离: 81.5335
  内存使用: 2.1711
  能量使用: 2.4779
  推理时间: 0.6530秒


==================== 总结 ====================
平均收益率: 0.9995
平均能量使用: 2.4901
平均推理时间: 0.5928秒
